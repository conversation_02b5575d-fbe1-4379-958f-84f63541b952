const r=new RegExp(["(\\\\.)","(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?"].join("|"),"g"),e=r=>r.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1"),t=r=>r.replace(/([=!:$/()])/g,"\\$1"),n=r=>r&&r.sensitive?"":"i",a=(r,t,a)=>{for(var o=(a=a||{}).strict,s=!1!==a.end,i=e(a.delimiter||"/"),l=a.delimiters||"./",c=[].concat(a.endsWith||[]).map(e).concat("$").join("|"),u="",f=!1,p=0;p<r.length;p++){var d=r[p];if("string"==typeof d)u+=e(d),f=p===r.length-1&&l.indexOf(d[d.length-1])>-1;else{var h=e(d.prefix||""),v=d.repeat?"(?:"+d.pattern+")(?:"+h+"(?:"+d.pattern+"))*":d.pattern;t&&t.push(d),u+=d.optional?d.partial?h+"("+v+")?":"(?:"+h+"("+v+"))?":h+"("+v+")"}}return s?(o||(u+="(?:"+i+")?"),u+="$"===c?"$":"(?="+c+")"):(o||(u+="(?:"+i+"(?="+c+"))?"),f||(u+="(?="+i+"|"+c+")")),new RegExp("^"+u,n(a))},o=(s,i,l)=>s instanceof RegExp?((r,e)=>{if(!e)return r;var t=r.source.match(/\((?!\?)/g);if(t)for(var n=0;n<t.length;n++)e.push({name:n,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,pattern:null});return r})(s,i):Array.isArray(s)?((r,e,t)=>{for(var a=[],s=0;s<r.length;s++)a.push(o(r[s],e,t).source);return new RegExp("(?:"+a.join("|")+")",n(t))})(s,i,l):((n,o,s)=>a(((n,a)=>{for(var o,s=[],i=0,l=0,c="",u=a&&a.delimiter||"/",f=a&&a.delimiters||"./",p=!1;null!==(o=r.exec(n));){var d=o[0],h=o[1],v=o.index;if(c+=n.slice(l,v),l=v+d.length,h)c+=h[1],p=!0;else{var g="",y=n[l],E=o[2],x=o[3],R=o[4],m=o[5];if(!p&&c.length){var $=c.length-1;f.indexOf(c[$])>-1&&(g=c[$],c=c.slice(0,$))}c&&(s.push(c),c="",p=!1);var O=g||u,_=x||R;s.push({name:E||i++,prefix:g,delimiter:O,optional:"?"===m||"*"===m,repeat:"+"===m||"*"===m,partial:""!==g&&void 0!==y&&y!==g,pattern:_?t(_):"[^"+e(O)+"]+?"})}}return(c||l<n.length)&&s.push(c+n.substr(l)),s})(n,s),o,s))(s,i,l),s=(r,e)=>new RegExp("^"+e+"(\\/|\\?|#|$)","i").test(r),i=(r,e)=>s(r,e)?r.substr(e.length):r,l=r=>"/"===r.charAt(r.length-1)?r.slice(0,-1):r,c=r=>"/"===r.charAt(0)?r:"/"+r,u=r=>"/"===r.charAt(0)?r.substr(1):r,f=r=>{const{pathname:e,search:t,hash:n}=r;let a=e||"/";return t&&"?"!==t&&(a+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(a+="#"===n.charAt(0)?n:"#"+n),a},p=r=>"/"===r.charAt(0),d=r=>Math.random().toString(36).substr(2,r),h=(r,e)=>{for(let t=e,n=t+1,a=r.length;n<a;t+=1,n+=1)r[t]=r[n];r.pop()},v=(r,e)=>{if(r===e)return!0;if(null==r||null==e)return!1;if(Array.isArray(r))return Array.isArray(e)&&r.length===e.length&&r.every((r,t)=>v(r,e[t]));const t=typeof r;if(t!==typeof e)return!1;if("object"===t){const t=r.valueOf(),n=e.valueOf();if(t!==r||n!==e)return v(t,n);const a=Object.keys(r),o=Object.keys(e);return a.length===o.length&&a.every(t=>v(r[t],e[t]))}return!1},g=(r,e)=>r.pathname===e.pathname&&r.search===e.search&&r.hash===e.hash&&r.key===e.key&&v(r.state,e.state),y=(r,e,t,n)=>{let a;"string"==typeof r?(a=(r=>{let e=r||"/",t="",n="";const a=e.indexOf("#");-1!==a&&(n=e.substr(a),e=e.substr(0,a));const o=e.indexOf("?");return-1!==o&&(t=e.substr(o),e=e.substr(0,o)),{pathname:e,search:"?"===t?"":t,hash:"#"===n?"":n,query:{},key:""}})(r),void 0!==e&&(a.state=e)):(a=Object.assign({pathname:""},r),a.search&&"?"!==a.search.charAt(0)&&(a.search="?"+a.search),a.hash&&"#"!==a.hash.charAt(0)&&(a.hash="#"+a.hash),void 0!==e&&void 0===a.state&&(a.state=e));try{a.pathname=decodeURI(a.pathname)}catch(r){throw r instanceof URIError?new URIError('Pathname "'+a.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):r}var o;return a.key=t,n?a.pathname?"/"!==a.pathname.charAt(0)&&(a.pathname=((r,e="")=>{let t,n=e&&e.split("/")||[],a=0;const o=r&&r.split("/")||[],s=r&&p(r),i=e&&p(e),l=s||i;if(r&&p(r)?n=o:o.length&&(n.pop(),n=n.concat(o)),!n.length)return"/";if(n.length){const r=n[n.length-1];t="."===r||".."===r||""===r}else t=!1;for(let r=n.length;r>=0;r--){const e=n[r];"."===e?h(n,r):".."===e?(h(n,r),a++):a&&(h(n,r),a--)}if(!l)for(;a--;a)n.unshift("..");!l||""===n[0]||n[0]&&p(n[0])||n.unshift("");let c=n.join("/");return t&&"/"!==c.substr(-1)&&(c+="/"),c})(a.pathname,n.pathname)):a.pathname=n.pathname:a.pathname||(a.pathname="/"),a.query=(o=a.search||"")?(/^[?#]/.test(o)?o.slice(1):o).split("&").reduce((r,e)=>{let[t,n]=e.split("=");return r[t]=n?decodeURIComponent(n.replace(/\+/g," ")):"",r},{}):{},a};let E=0;const x={},R=(r,e={})=>{"string"==typeof e&&(e={path:e});const{path:t="/",exact:n=!1,strict:a=!1}=e,{re:s,keys:i}=((r,e)=>{const t=`${e.end}${e.strict}`,n=x[t]||(x[t]={}),a=JSON.stringify(r);if(n[a])return n[a];const s=[],i={re:o(r,s,e),keys:s};return E<1e4&&(n[a]=i,E+=1),i})(t,{end:n,strict:a}),l=s.exec(r);if(!l)return null;const[c,...u]=l,f=r===c;return n&&!f?null:{path:t,url:"/"===t&&""===c?"/":c,isExact:f,params:i.reduce((r,e,t)=>(r[e.name]=u[t],r),{})}},m=(r,e)=>null==r&&null==e||null!=e&&r&&e&&r.path===e.path&&r.url===e.url&&v(r.params,e.params),$=(r,e,t)=>t(r.confirm(e)),O=r=>r.metaKey||r.altKey||r.ctrlKey||r.shiftKey,_=r=>{const e=r.navigator.userAgent;return(-1===e.indexOf("Android 2.")&&-1===e.indexOf("Android 4.0")||-1===e.indexOf("Mobile Safari")||-1!==e.indexOf("Chrome")||-1!==e.indexOf("Windows Phone"))&&r.history&&"pushState"in r.history},b=r=>-1===r.userAgent.indexOf("Trident"),w=r=>-1===r.userAgent.indexOf("Firefox"),A=(r,e)=>void 0===e.state&&-1===r.userAgent.indexOf("CriOS"),j=(r,e)=>{const t=r[e],n="__storage_test__";try{return t.setItem(n,n),t.removeItem(n),!0}catch(r){return r instanceof DOMException&&(22===r.code||1014===r.code||"QuotaExceededError"===r.name||"NS_ERROR_DOM_QUOTA_REACHED"===r.name)&&0!==t.length}};export{m as a,_ as b,b as c,l as d,c as e,y as f,d as g,s as h,i,f as j,$ as k,A as l,R as m,w as n,u as o,g as p,O as q,j as s}