{"version": 3, "names": ["_fs", "data", "require", "commander", "_interopRequireWildcard", "_core", "glob", "_util", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "program", "option", "collect", "booleanify", "undefined", "DEFAULT_EXTENSIONS", "join", "version", "usage", "action", "parseArgv", "args", "parse", "opts", "errors", "filenames", "reduce", "globbed", "input", "files", "sync", "length", "push", "Array", "from", "Set", "for<PERSON>ach", "filename", "fs", "existsSync", "outDir", "outFile", "relative", "watch", "skipInitialBuild", "deleteDirOnStart", "verbose", "quiet", "babelrc", "keepFileExtension", "outFileExtension", "console", "error", "babelOptions", "presets", "plugins", "rootMode", "configFile", "envName", "sourceType", "ignore", "only", "retainLines", "compact", "minified", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "sourceMaps", "sourceFileName", "sourceRoot", "highlightCode", "comments", "assign", "moduleRoot", "moduleIds", "moduleId", "key", "keys", "cliOptions", "extensions", "copyFiles", "copyIgnored", "includeDotfiles", "sourceMapTarget", "val", "value", "previousValue", "values", "split"], "sources": ["../../src/babel/options.ts"], "sourcesContent": ["import fs from \"node:fs\";\n\nimport * as commander from \"commander\";\nimport { version, DEFAULT_EXTENSIONS } from \"@babel/core\";\nimport * as glob from \"glob\";\nimport { alphasort } from \"./util.ts\";\n\nimport type { InputOptions } from \"@babel/core\";\n\nconst program = process.env.BABEL_8_BREAKING\n  ? commander.program\n  : commander.default.program;\n\n// Standard Babel input configs.\nprogram.option(\n  \"-f, --filename [filename]\",\n  \"The filename to use when reading from stdin. This will be used in source-maps, errors etc.\",\n);\nprogram.option(\n  \"--presets [list]\",\n  \"A comma-separated list of preset names.\",\n  collect,\n);\nprogram.option(\n  \"--plugins [list]\",\n  \"A comma-separated list of plugin names.\",\n  collect,\n);\nprogram.option(\"--config-file [path]\", \"Path to a .babelrc file to use.\");\nprogram.option(\n  \"--env-name [name]\",\n  \"The name of the 'env' to use when loading configs and plugins. \" +\n    \"Defaults to the value of BABEL_ENV, or else NODE_ENV, or else 'development'.\",\n);\nprogram.option(\n  \"--root-mode [mode]\",\n  \"The project-root resolution mode. \" +\n    \"One of 'root' (the default), 'upward', or 'upward-optional'.\",\n);\n\n// Basic file input configuration.\nprogram.option(\"--source-type [script|module]\", \"\");\nprogram.option(\n  \"--no-babelrc\",\n  \"Whether or not to look up .babelrc and .babelignore files.\",\n);\nprogram.option(\n  \"--ignore [list]\",\n  \"List of glob paths to **not** compile.\",\n  collect,\n);\nprogram.option(\n  \"--only [list]\",\n  \"List of glob paths to **only** compile.\",\n  collect,\n);\n\n// Misc babel config.\nprogram.option(\n  \"--no-highlight-code\",\n  \"Enable or disable ANSI syntax highlighting of code frames. (on by default)\",\n);\n\n// General output formatting.\nprogram.option(\n  \"--no-comments\",\n  \"Write comments to generated output. (true by default)\",\n);\nprogram.option(\n  \"--retain-lines\",\n  \"Retain line numbers. This will result in really ugly code.\",\n);\nprogram.option(\n  \"--compact [true|false|auto]\",\n  \"Do not include superfluous whitespace characters and line terminators.\",\n  booleanify,\n);\nprogram.option(\n  \"--minified\",\n  \"Save as many bytes when printing. (false by default)\",\n);\nprogram.option(\n  \"--auxiliary-comment-before [string]\",\n  \"Print a comment before any injected non-user code.\",\n);\nprogram.option(\n  \"--auxiliary-comment-after [string]\",\n  \"Print a comment after any injected non-user code.\",\n);\n\n// General source map formatting.\nprogram.option(\n  \"-s, --source-maps [true|false|inline|both]\",\n  \"\",\n  booleanify,\n  undefined,\n);\nprogram.option(\n  \"--source-map-target [string]\",\n  \"Set `file` on returned source map.\",\n);\nprogram.option(\n  \"--source-file-name [string]\",\n  \"Set `sources[0]` on returned source map.\",\n);\nprogram.option(\n  \"--source-root [filename]\",\n  \"The root from which all sources are relative.\",\n);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // Config params for certain module output formats.\n  program.option(\n    \"--module-root [filename]\",\n    \"Optional prefix for the AMD module formatter that will be prepended to the filename on module definitions.\",\n  );\n  program.option(\"-M, --module-ids\", \"Insert an explicit id for modules.\");\n  program.option(\n    \"--module-id [string]\",\n    \"Specify a custom name for module ids.\",\n  );\n}\n\n// \"babel\" command specific arguments that are not passed to @babel/core.\nprogram.option(\n  \"-x, --extensions [extensions]\",\n  \"List of extensions to compile when a directory has been the input. [\" +\n    DEFAULT_EXTENSIONS.join() +\n    \"]\",\n  collect,\n);\nprogram.option(\n  \"--keep-file-extension\",\n  \"Preserve the file extensions of the input files.\",\n);\nprogram.option(\"-w, --watch\", \"Recompile files on changes.\");\nprogram.option(\"--skip-initial-build\", \"Do not compile files before watching.\");\nprogram.option(\n  \"-o, --out-file [out]\",\n  \"Compile all input files into a single file.\",\n);\nprogram.option(\n  \"-d, --out-dir [out]\",\n  \"Compile an input directory of modules into an output directory.\",\n);\nprogram.option(\n  \"--relative\",\n  \"Compile into an output directory relative to input directory or file. Requires --out-dir [out]\",\n);\n\nprogram.option(\n  \"-D, --copy-files\",\n  \"When compiling a directory copy over non-compilable files.\",\n);\nprogram.option(\n  \"--include-dotfiles\",\n  \"Include dotfiles when compiling and copying non-compilable files.\",\n);\nprogram.option(\n  \"--no-copy-ignored\",\n  \"Exclude ignored files when copying non-compilable files.\",\n);\n\nprogram.option(\n  \"--verbose\",\n  \"Log everything. This option conflicts with --quiet\",\n);\nprogram.option(\n  \"--quiet\",\n  \"Don't log anything. This option conflicts with --verbose\",\n);\nprogram.option(\n  \"--delete-dir-on-start\",\n  \"Delete the out directory before compilation.\",\n);\nprogram.option(\n  \"--out-file-extension [string]\",\n  \"Use a specific extension for the output files\",\n);\n\nprogram.version(PACKAGE_JSON.version + \" (@babel/core \" + version + \")\");\nprogram.usage(\"[options] <files ...>\");\n// register an empty action handler so that program.js can throw on\n// unknown options _after_ args\n// see https://github.com/tj/program.js/issues/561#issuecomment-522209408\nprogram.action(() => {});\n\nexport type CmdOptions = {\n  babelOptions: InputOptions;\n  cliOptions: {\n    filename: string;\n    filenames: string[];\n    extensions: string[];\n    keepFileExtension: boolean;\n    outFileExtension: string;\n    watch: boolean;\n    skipInitialBuild: boolean;\n    outFile: string;\n    outDir: string;\n    relative: boolean;\n    copyFiles: boolean;\n    copyIgnored: boolean;\n    includeDotfiles: boolean;\n    verbose: boolean;\n    quiet: boolean;\n    deleteDirOnStart: boolean;\n    sourceMapTarget: string;\n  };\n};\n\nexport default function parseArgv(args: Array<string>): CmdOptions | null {\n  //\n  program.parse(args);\n\n  const opts = program.opts();\n\n  const errors: string[] = [];\n\n  let filenames = program.args.reduce(function (globbed: string[], input) {\n    let files = process.env.BABEL_8_BREAKING\n      ? // glob 9+ no longer sorts the result, here we maintain the glob 7 behaviour\n        // https://github.com/isaacs/node-glob/blob/c3cd57ae128faa0e9190492acc743bb779ac4054/common.js#L151\n        glob.sync(input, { dotRelative: true }).sort(alphasort)\n      : // When USE_ESM is true and BABEL_8_BREAKING is off,\n        // the glob package is an ESM wrapper of the CJS glob 7\n        (USE_ESM ? glob.default.sync : glob.sync)(input);\n    if (!files.length) files = [input];\n    globbed.push(...files);\n    return globbed;\n  }, []);\n\n  filenames = Array.from(new Set(filenames));\n\n  filenames.forEach(function (filename) {\n    if (!fs.existsSync(filename)) {\n      errors.push(filename + \" does not exist\");\n    }\n  });\n\n  if (opts.outDir && !filenames.length) {\n    errors.push(\"--out-dir requires filenames\");\n  }\n\n  if (opts.outFile && opts.outDir) {\n    errors.push(\"--out-file and --out-dir cannot be used together\");\n  }\n\n  if (opts.relative && !opts.outDir) {\n    errors.push(\"--relative requires --out-dir usage\");\n  }\n\n  if (opts.watch) {\n    if (!opts.outFile && !opts.outDir) {\n      errors.push(\"--watch requires --out-file or --out-dir\");\n    }\n\n    if (!filenames.length) {\n      errors.push(\"--watch requires filenames\");\n    }\n  }\n\n  if (opts.skipInitialBuild && !opts.watch) {\n    errors.push(\"--skip-initial-build requires --watch\");\n  }\n  if (opts.deleteDirOnStart && !opts.outDir) {\n    errors.push(\"--delete-dir-on-start requires --out-dir\");\n  }\n\n  if (opts.verbose && opts.quiet) {\n    errors.push(\"--verbose and --quiet cannot be used together\");\n  }\n\n  if (\n    !opts.outDir &&\n    filenames.length === 0 &&\n    typeof opts.filename !== \"string\" &&\n    opts.babelrc !== false\n  ) {\n    errors.push(\n      \"stdin compilation requires either -f/--filename [filename] or --no-babelrc\",\n    );\n  }\n\n  if (opts.keepFileExtension && opts.outFileExtension) {\n    errors.push(\n      \"--out-file-extension cannot be used with --keep-file-extension\",\n    );\n  }\n\n  if (errors.length) {\n    console.error(\"babel:\");\n    errors.forEach(function (e) {\n      console.error(\"  \" + e);\n    });\n    return null;\n  }\n\n  const babelOptions: InputOptions = {\n    presets: opts.presets,\n    plugins: opts.plugins,\n    rootMode: opts.rootMode,\n    configFile: opts.configFile,\n    envName: opts.envName,\n    sourceType: opts.sourceType,\n    ignore: opts.ignore,\n    only: opts.only,\n    retainLines: opts.retainLines,\n    compact: opts.compact,\n    minified: opts.minified,\n    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n    sourceMaps: opts.sourceMaps,\n    sourceFileName: opts.sourceFileName,\n    sourceRoot: opts.sourceRoot,\n\n    // Commander will default the \"--no-\" arguments to true, but we want to\n    // leave them undefined so that @babel/core can handle the\n    // default-assignment logic on its own.\n    babelrc: opts.babelrc === true ? undefined : opts.babelrc,\n    highlightCode: opts.highlightCode === true ? undefined : opts.highlightCode,\n    comments: opts.comments === true ? undefined : opts.comments,\n  };\n\n  if (!process.env.BABEL_8_BREAKING) {\n    Object.assign(babelOptions, {\n      moduleRoot: opts.moduleRoot,\n      moduleIds: opts.moduleIds,\n      moduleId: opts.moduleId,\n    });\n  }\n\n  // If the @babel/cli version is newer than the @babel/core version, and we have added\n  // new options for @babel/core, we'll potentially get option validation errors from\n  // @babel/core. To avoid that, we delete undefined options, so @babel/core will only\n  // give the error if users actually pass an unsupported CLI option.\n  for (const key of Object.keys(babelOptions) as Array<\n    keyof typeof babelOptions\n  >) {\n    if (babelOptions[key] === undefined) {\n      delete babelOptions[key];\n    }\n  }\n\n  return {\n    babelOptions,\n    cliOptions: {\n      filename: opts.filename,\n      filenames,\n      extensions: opts.extensions,\n      keepFileExtension: opts.keepFileExtension,\n      outFileExtension: opts.outFileExtension,\n      watch: opts.watch,\n      skipInitialBuild: opts.skipInitialBuild,\n      outFile: opts.outFile,\n      outDir: opts.outDir,\n      relative: opts.relative,\n      copyFiles: opts.copyFiles,\n      copyIgnored: opts.copyFiles && opts.copyIgnored,\n      includeDotfiles: opts.includeDotfiles,\n      verbose: opts.verbose,\n      quiet: opts.quiet,\n      deleteDirOnStart: opts.deleteDirOnStart,\n      sourceMapTarget: opts.sourceMapTarget,\n    },\n  };\n}\n\nfunction booleanify(val: \"false\" | \"0\" | \"\"): false;\nfunction booleanify(val: \"true\" | \"1\"): true;\nfunction booleanify(val: string): boolean | string {\n  if (val === \"true\" || val === \"1\") {\n    return true;\n  }\n\n  // false for --opt=false; 0 for --opt=0 or --opt 0; \"\" for --opt=\n  if (val === \"false\" || val === \"0\" || val === \"\") {\n    return false;\n  }\n\n  return val;\n}\n\nfunction collect(value: unknown, previousValue: Array<string>): Array<string> {\n  // If the user passed the option with no value, like \"babel file.js --presets\", do nothing.\n  if (typeof value !== \"string\") return previousValue;\n\n  const values = value.split(\",\");\n\n  if (previousValue) {\n    previousValue.push(...values);\n    return previousValue;\n  }\n  return values;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAI,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAM,KAAA,GAAAL,OAAA;AAAsC,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAItC,MAAMW,OAAO,GAETzB,SAAS,CAAD,CAAC,CAACW,OAAO,CAACc,OAAO;AAG7BA,OAAO,CAACC,MAAM,CACZ,2BAA2B,EAC3B,4FACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,kBAAkB,EAClB,yCAAyC,EACzCC,OACF,CAAC;AACDF,OAAO,CAACC,MAAM,CACZ,kBAAkB,EAClB,yCAAyC,EACzCC,OACF,CAAC;AACDF,OAAO,CAACC,MAAM,CAAC,sBAAsB,EAAE,iCAAiC,CAAC;AACzED,OAAO,CAACC,MAAM,CACZ,mBAAmB,EACnB,iEAAiE,GAC/D,8EACJ,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,oBAAoB,EACpB,oCAAoC,GAClC,8DACJ,CAAC;AAGDD,OAAO,CAACC,MAAM,CAAC,+BAA+B,EAAE,EAAE,CAAC;AACnDD,OAAO,CAACC,MAAM,CACZ,cAAc,EACd,4DACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,iBAAiB,EACjB,wCAAwC,EACxCC,OACF,CAAC;AACDF,OAAO,CAACC,MAAM,CACZ,eAAe,EACf,yCAAyC,EACzCC,OACF,CAAC;AAGDF,OAAO,CAACC,MAAM,CACZ,qBAAqB,EACrB,4EACF,CAAC;AAGDD,OAAO,CAACC,MAAM,CACZ,eAAe,EACf,uDACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,gBAAgB,EAChB,4DACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,6BAA6B,EAC7B,wEAAwE,EACxEE,UACF,CAAC;AACDH,OAAO,CAACC,MAAM,CACZ,YAAY,EACZ,sDACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,qCAAqC,EACrC,oDACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,oCAAoC,EACpC,mDACF,CAAC;AAGDD,OAAO,CAACC,MAAM,CACZ,4CAA4C,EAC5C,EAAE,EACFE,UAAU,EACVC,SACF,CAAC;AACDJ,OAAO,CAACC,MAAM,CACZ,8BAA8B,EAC9B,oCACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,6BAA6B,EAC7B,0CACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,0BAA0B,EAC1B,+CACF,CAAC;AAEkC;EAEjCD,OAAO,CAACC,MAAM,CACZ,0BAA0B,EAC1B,4GACF,CAAC;EACDD,OAAO,CAACC,MAAM,CAAC,kBAAkB,EAAE,oCAAoC,CAAC;EACxED,OAAO,CAACC,MAAM,CACZ,sBAAsB,EACtB,uCACF,CAAC;AACH;AAGAD,OAAO,CAACC,MAAM,CACZ,+BAA+B,EAC/B,sEAAsE,GACpEI,0BAAkB,CAACC,IAAI,CAAC,CAAC,GACzB,GAAG,EACLJ,OACF,CAAC;AACDF,OAAO,CAACC,MAAM,CACZ,uBAAuB,EACvB,kDACF,CAAC;AACDD,OAAO,CAACC,MAAM,CAAC,aAAa,EAAE,6BAA6B,CAAC;AAC5DD,OAAO,CAACC,MAAM,CAAC,sBAAsB,EAAE,uCAAuC,CAAC;AAC/ED,OAAO,CAACC,MAAM,CACZ,sBAAsB,EACtB,6CACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,qBAAqB,EACrB,iEACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,YAAY,EACZ,gGACF,CAAC;AAEDD,OAAO,CAACC,MAAM,CACZ,kBAAkB,EAClB,4DACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,oBAAoB,EACpB,mEACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,mBAAmB,EACnB,0DACF,CAAC;AAEDD,OAAO,CAACC,MAAM,CACZ,WAAW,EACX,oDACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,SAAS,EACT,0DACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,uBAAuB,EACvB,8CACF,CAAC;AACDD,OAAO,CAACC,MAAM,CACZ,+BAA+B,EAC/B,+CACF,CAAC;AAEDD,OAAO,CAACO,OAAO,CAAC,WAAuB,gBAAgB,GAAGA,eAAO,GAAG,GAAG,CAAC;AACxEP,OAAO,CAACQ,KAAK,CAAC,uBAAuB,CAAC;AAItCR,OAAO,CAACS,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAyBT,SAASC,SAASA,CAACC,IAAmB,EAAqB;EAExEX,OAAO,CAACY,KAAK,CAACD,IAAI,CAAC;EAEnB,MAAME,IAAI,GAAGb,OAAO,CAACa,IAAI,CAAC,CAAC;EAE3B,MAAMC,MAAgB,GAAG,EAAE;EAE3B,IAAIC,SAAS,GAAGf,OAAO,CAACW,IAAI,CAACK,MAAM,CAAC,UAAUC,OAAiB,EAAEC,KAAK,EAAE;IACtE,IAAIC,KAAK,GAM0BzC,IAAI,CAAD,CAAC,CAAC0C,IAAI,CAAEF,KAAK,CAAC;IACpD,IAAI,CAACC,KAAK,CAACE,MAAM,EAAEF,KAAK,GAAG,CAACD,KAAK,CAAC;IAClCD,OAAO,CAACK,IAAI,CAAC,GAAGH,KAAK,CAAC;IACtB,OAAOF,OAAO;EAChB,CAAC,EAAE,EAAE,CAAC;EAENF,SAAS,GAAGQ,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACV,SAAS,CAAC,CAAC;EAE1CA,SAAS,CAACW,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACpC,IAAI,CAACC,IAACA,CAAC,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE;MAC5Bb,MAAM,CAACQ,IAAI,CAACK,QAAQ,GAAG,iBAAiB,CAAC;IAC3C;EACF,CAAC,CAAC;EAEF,IAAId,IAAI,CAACiB,MAAM,IAAI,CAACf,SAAS,CAACM,MAAM,EAAE;IACpCP,MAAM,CAACQ,IAAI,CAAC,8BAA8B,CAAC;EAC7C;EAEA,IAAIT,IAAI,CAACkB,OAAO,IAAIlB,IAAI,CAACiB,MAAM,EAAE;IAC/BhB,MAAM,CAACQ,IAAI,CAAC,kDAAkD,CAAC;EACjE;EAEA,IAAIT,IAAI,CAACmB,QAAQ,IAAI,CAACnB,IAAI,CAACiB,MAAM,EAAE;IACjChB,MAAM,CAACQ,IAAI,CAAC,qCAAqC,CAAC;EACpD;EAEA,IAAIT,IAAI,CAACoB,KAAK,EAAE;IACd,IAAI,CAACpB,IAAI,CAACkB,OAAO,IAAI,CAAClB,IAAI,CAACiB,MAAM,EAAE;MACjChB,MAAM,CAACQ,IAAI,CAAC,0CAA0C,CAAC;IACzD;IAEA,IAAI,CAACP,SAAS,CAACM,MAAM,EAAE;MACrBP,MAAM,CAACQ,IAAI,CAAC,4BAA4B,CAAC;IAC3C;EACF;EAEA,IAAIT,IAAI,CAACqB,gBAAgB,IAAI,CAACrB,IAAI,CAACoB,KAAK,EAAE;IACxCnB,MAAM,CAACQ,IAAI,CAAC,uCAAuC,CAAC;EACtD;EACA,IAAIT,IAAI,CAACsB,gBAAgB,IAAI,CAACtB,IAAI,CAACiB,MAAM,EAAE;IACzChB,MAAM,CAACQ,IAAI,CAAC,0CAA0C,CAAC;EACzD;EAEA,IAAIT,IAAI,CAACuB,OAAO,IAAIvB,IAAI,CAACwB,KAAK,EAAE;IAC9BvB,MAAM,CAACQ,IAAI,CAAC,+CAA+C,CAAC;EAC9D;EAEA,IACE,CAACT,IAAI,CAACiB,MAAM,IACZf,SAAS,CAACM,MAAM,KAAK,CAAC,IACtB,OAAOR,IAAI,CAACc,QAAQ,KAAK,QAAQ,IACjCd,IAAI,CAACyB,OAAO,KAAK,KAAK,EACtB;IACAxB,MAAM,CAACQ,IAAI,CACT,4EACF,CAAC;EACH;EAEA,IAAIT,IAAI,CAAC0B,iBAAiB,IAAI1B,IAAI,CAAC2B,gBAAgB,EAAE;IACnD1B,MAAM,CAACQ,IAAI,CACT,gEACF,CAAC;EACH;EAEA,IAAIR,MAAM,CAACO,MAAM,EAAE;IACjBoB,OAAO,CAACC,KAAK,CAAC,QAAQ,CAAC;IACvB5B,MAAM,CAACY,OAAO,CAAC,UAAU7C,CAAC,EAAE;MAC1B4D,OAAO,CAACC,KAAK,CAAC,IAAI,GAAG7D,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEA,MAAM8D,YAA0B,GAAG;IACjCC,OAAO,EAAE/B,IAAI,CAAC+B,OAAO;IACrBC,OAAO,EAAEhC,IAAI,CAACgC,OAAO;IACrBC,QAAQ,EAAEjC,IAAI,CAACiC,QAAQ;IACvBC,UAAU,EAAElC,IAAI,CAACkC,UAAU;IAC3BC,OAAO,EAAEnC,IAAI,CAACmC,OAAO;IACrBC,UAAU,EAAEpC,IAAI,CAACoC,UAAU;IAC3BC,MAAM,EAAErC,IAAI,CAACqC,MAAM;IACnBC,IAAI,EAAEtC,IAAI,CAACsC,IAAI;IACfC,WAAW,EAAEvC,IAAI,CAACuC,WAAW;IAC7BC,OAAO,EAAExC,IAAI,CAACwC,OAAO;IACrBC,QAAQ,EAAEzC,IAAI,CAACyC,QAAQ;IACvBC,sBAAsB,EAAE1C,IAAI,CAAC0C,sBAAsB;IACnDC,qBAAqB,EAAE3C,IAAI,CAAC2C,qBAAqB;IACjDC,UAAU,EAAE5C,IAAI,CAAC4C,UAAU;IAC3BC,cAAc,EAAE7C,IAAI,CAAC6C,cAAc;IACnCC,UAAU,EAAE9C,IAAI,CAAC8C,UAAU;IAK3BrB,OAAO,EAAEzB,IAAI,CAACyB,OAAO,KAAK,IAAI,GAAGlC,SAAS,GAAGS,IAAI,CAACyB,OAAO;IACzDsB,aAAa,EAAE/C,IAAI,CAAC+C,aAAa,KAAK,IAAI,GAAGxD,SAAS,GAAGS,IAAI,CAAC+C,aAAa;IAC3EC,QAAQ,EAAEhD,IAAI,CAACgD,QAAQ,KAAK,IAAI,GAAGzD,SAAS,GAAGS,IAAI,CAACgD;EACtD,CAAC;EAEkC;IACjCrE,MAAM,CAACsE,MAAM,CAACnB,YAAY,EAAE;MAC1BoB,UAAU,EAAElD,IAAI,CAACkD,UAAU;MAC3BC,SAAS,EAAEnD,IAAI,CAACmD,SAAS;MACzBC,QAAQ,EAAEpD,IAAI,CAACoD;IACjB,CAAC,CAAC;EACJ;EAMA,KAAK,MAAMC,GAAG,IAAI1E,MAAM,CAAC2E,IAAI,CAACxB,YAAY,CAAC,EAExC;IACD,IAAIA,YAAY,CAACuB,GAAG,CAAC,KAAK9D,SAAS,EAAE;MACnC,OAAOuC,YAAY,CAACuB,GAAG,CAAC;IAC1B;EACF;EAEA,OAAO;IACLvB,YAAY;IACZyB,UAAU,EAAE;MACVzC,QAAQ,EAAEd,IAAI,CAACc,QAAQ;MACvBZ,SAAS;MACTsD,UAAU,EAAExD,IAAI,CAACwD,UAAU;MAC3B9B,iBAAiB,EAAE1B,IAAI,CAAC0B,iBAAiB;MACzCC,gBAAgB,EAAE3B,IAAI,CAAC2B,gBAAgB;MACvCP,KAAK,EAAEpB,IAAI,CAACoB,KAAK;MACjBC,gBAAgB,EAAErB,IAAI,CAACqB,gBAAgB;MACvCH,OAAO,EAAElB,IAAI,CAACkB,OAAO;MACrBD,MAAM,EAAEjB,IAAI,CAACiB,MAAM;MACnBE,QAAQ,EAAEnB,IAAI,CAACmB,QAAQ;MACvBsC,SAAS,EAAEzD,IAAI,CAACyD,SAAS;MACzBC,WAAW,EAAE1D,IAAI,CAACyD,SAAS,IAAIzD,IAAI,CAAC0D,WAAW;MAC/CC,eAAe,EAAE3D,IAAI,CAAC2D,eAAe;MACrCpC,OAAO,EAAEvB,IAAI,CAACuB,OAAO;MACrBC,KAAK,EAAExB,IAAI,CAACwB,KAAK;MACjBF,gBAAgB,EAAEtB,IAAI,CAACsB,gBAAgB;MACvCsC,eAAe,EAAE5D,IAAI,CAAC4D;IACxB;EACF,CAAC;AACH;AAIA,SAAStE,UAAUA,CAACuE,GAAW,EAAoB;EACjD,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,GAAG,EAAE;IACjC,OAAO,IAAI;EACb;EAGA,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,EAAE,EAAE;IAChD,OAAO,KAAK;EACd;EAEA,OAAOA,GAAG;AACZ;AAEA,SAASxE,OAAOA,CAACyE,KAAc,EAAEC,aAA4B,EAAiB;EAE5E,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE,OAAOC,aAAa;EAEnD,MAAMC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;EAE/B,IAAIF,aAAa,EAAE;IACjBA,aAAa,CAACtD,IAAI,CAAC,GAAGuD,MAAM,CAAC;IAC7B,OAAOD,aAAa;EACtB;EACA,OAAOC,MAAM;AACf", "ignoreList": []}