<?php
// Include functions if not already included
if (!function_exists('getSetting')) {
    require_once 'includes/functions.php';
}

// Set default page title and description if not set
$pageTitle = isset($pageTitle) ? $pageTitle : 'Home';
$pageDescription = isset($pageDescription) ? $pageDescription : 'Arcke Interior Design - Professional interior design services for homes, offices, and commercial spaces. Expert architecture, decoration, and 2D/3D layouts.';
$isHomePage = isset($isHomePage) ? $isHomePage : false;

// Get navigation items and social links
$navItems = getNavigationItems();
$socialLinks = getSocialMediaLinks();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="interior design, architecture, home decoration, office design, 2D layouts, 3D layouts, construction">
    <meta name="author" content="Arcke Interior Design">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($pageTitle); ?> - Professional Interior Design Services">
    <meta property="og:description" content="Transform your space with our expert interior design services. Specializing in architecture, decoration, and custom layouts.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="assets/images/logo.png">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($pageTitle); ?> - Professional Interior Design Services">
    <meta name="twitter:description" content="Transform your space with our expert interior design services.">
    <meta name="twitter:image" content="assets/images/logo.png">

    <title><?php echo htmlspecialchars($pageTitle); ?> - Professional Interior Design Services | Architecture & Home Decoration</title>

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Josefin+Sans:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" as="style">
    <link rel="preload" href="css/home.css" as="style">
    <?php if ($isHomePage): ?>
    <link rel="preload" href="assets/images/front.jpg" as="image">
    <?php endif; ?>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Josefin+Sans:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- SwiperJS for Carousel -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" />

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/home.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Arcke Interior Design",
        "description": "Professional interior design consultancy firm specializing in architecture, decoration, and custom layouts.",
        "url": "<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST']; ?>",
        "logo": "<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST']; ?>/assets/images/logo.png",
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+************",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "14/A, Miranda City",
            "addressLocality": "NYC",
            "addressCountry": "US"
        },
        "sameAs": [
            "<?php echo $socialLinks['facebook']['url']; ?>",
            "<?php echo $socialLinks['twitter']['url']; ?>",
            "<?php echo $socialLinks['linkedin']['url']; ?>",
            "<?php echo $socialLinks['pinterest']['url']; ?>"
        ]
    }
    </script>

    <style>
        /* Light Header Styles */
        .main-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .main-header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .main-nav a {
            color: #374151;
            transition: color 0.3s ease;
            text-decoration: none;
            position: relative;
        }

        .main-nav a:hover,
        .main-nav a.active {
            color: #fb923c;
        }

        .main-nav a.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            right: 0;
            height: 2px;
            background: #fb923c;
            border-radius: 1px;
        }

        .mobile-nav {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .mobile-nav.show {
            transform: translateY(0) !important;
            opacity: 1 !important;
        }

        /* Smooth animations */
        * {
            box-sizing: border-box;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #fb923c;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #ea7c2b;
        }
    </style>
</head>

<body>


    <!-- Header -->
    <header class="main-header" role="banner">
        <div class="header-container">
            <a href="<?php echo $isHomePage ? '#home' : 'index.php'; ?>" class="logo" aria-label="Arcke Interior Design - Go to homepage">
                <img src="assets/images/logo.png" alt="Arcke Interior Design Logo" width="160" height="auto">
            </a>
            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <?php echo renderNavigation($navItems, $isHomePage); ?>
            </nav>
            <a href="<?php echo $isHomePage ? '#contact' : 'contact.php'; ?>" class="btn btn-quote" aria-label="Get a quote for your project">GET A QUOTE</a>
            <button class="mobile-nav-toggle" aria-label="Toggle mobile navigation" aria-expanded="false">
                <i class="fas fa-bars" aria-hidden="true"></i>
                <span class="sr-only">Menu</span>
            </button>
        </div>
    </header>

    <!-- Page Content Starts Here -->

<script>
// Enhanced header functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    const mobileNav = document.querySelector('.mobile-nav');
    const header = document.querySelector('.main-header');

    // Mobile menu toggle functionality
    if (mobileNavToggle && mobileNav) {
        mobileNavToggle.addEventListener('click', function(e) {
            e.preventDefault();
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            if (isExpanded) {
                // Close menu
                mobileNav.classList.remove('show');
                mobileNav.style.transform = 'translateY(-100%)';
                mobileNav.style.opacity = '0';
                this.setAttribute('aria-expanded', 'false');
                this.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i><span class="sr-only">Menu</span>';
            } else {
                // Open menu
                mobileNav.classList.add('show');
                mobileNav.style.transform = 'translateY(0)';
                mobileNav.style.opacity = '1';
                this.setAttribute('aria-expanded', 'true');
                this.innerHTML = '<i class="fas fa-times" aria-hidden="true"></i><span class="sr-only">Close menu</span>';
            }
        });

        // Close mobile menu when clicking on a link
        const mobileLinks = mobileNav.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileNav.classList.remove('show');
                mobileNav.style.transform = 'translateY(-100%)';
                mobileNav.style.opacity = '0';
                mobileNavToggle.setAttribute('aria-expanded', 'false');
                mobileNavToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i><span class="sr-only">Menu</span>';
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileNav.contains(e.target) && !mobileNavToggle.contains(e.target)) {
                if (mobileNavToggle.getAttribute('aria-expanded') === 'true') {
                    mobileNav.classList.remove('show');
                    mobileNav.style.transform = 'translateY(-100%)';
                    mobileNav.style.opacity = '0';
                    mobileNavToggle.setAttribute('aria-expanded', 'false');
                    mobileNavToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i><span class="sr-only">Menu</span>';
                }
            }
        });
    }

    // Header scroll effect with throttling
    if (header) {
        let ticking = false;

        function updateHeader() {
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateHeader);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);
    }

    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href !== '#' && href.length > 1) {
                const target = document.querySelector(href);
                if (target) {
                    e.preventDefault();
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
});
</script> 