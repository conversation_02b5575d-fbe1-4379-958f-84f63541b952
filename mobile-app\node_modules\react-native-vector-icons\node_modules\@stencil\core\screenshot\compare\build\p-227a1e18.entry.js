import{r as t,h as s,g as e,c as o}from"./p-fbbae598.js";import{A as i}from"./p-e2efe0df.js";import{m as n,a as r,s as a,b as h,c,d as l,e as u,f as p,g as d,h as f,i as g,j as y,k as m,l as b,n as w,o as P,p as v}from"./p-9b6a9315.js";class O{constructor(s){t(this,s)}render(){return s("stencil-router",{class:"full-screen"},s("stencil-route-switch",null,s("stencil-route",{url:"/:buildIdA/:buildIdB",exact:!0,component:"screenshot-compare",class:"full-screen"}),s("stencil-route",{url:"/:buildId",component:"screenshot-preview",class:"full-screen"}),s("stencil-route",{url:"/",component:"screenshot-preview",exact:!0,class:"full-screen"})))}}class j{constructor(s){t(this,s),this.group=null,this.match=null,this.componentProps={},this.exact=!1,this.scrollOnNextRender=!1,this.previousMatch=null}computeMatch(t){const s=null!=this.group||null!=this.el.parentElement&&"stencil-route-switch"===this.el.parentElement.tagName.toLowerCase();if(t&&!s)return this.previousMatch=this.match,this.match=n(t.pathname,{path:this.url,exact:this.exact,strict:!0})}async loadCompleted(){let t={};this.history&&this.history.location.hash?t={scrollToId:this.history.location.hash.substr(1)}:this.scrollTopOffset&&(t={scrollTopOffset:this.scrollTopOffset}),"function"==typeof this.componentUpdated?this.componentUpdated(t):this.match&&!r(this.match,this.previousMatch)&&this.routeViewsUpdated&&this.routeViewsUpdated(t)}async componentDidUpdate(){await this.loadCompleted()}async componentDidLoad(){await this.loadCompleted()}render(){if(!this.match||!this.history)return null;const t=Object.assign({},this.componentProps,{history:this.history,match:this.match});return this.routeRender?this.routeRender(Object.assign({},t,{component:this.component})):this.component?s(this.component,Object.assign({},t)):void 0}get el(){return e(this)}static get watchers(){return{location:["computeMatch"]}}}i.injectProps(j,["location","history","historyType","routeViewsUpdated"]),j.style="stencil-route.inactive{display:none}";const L=t=>"STENCIL-ROUTE"===t.tagName;class S{constructor(s){t(this,s),this.group=((1e17*Math.random()).toString().match(/.{4}/g)||[]).join("-"),this.subscribers=[],this.queue=o(this,"queue")}componentWillLoad(){null!=this.location&&this.regenerateSubscribers(this.location)}async regenerateSubscribers(t){if(null==t)return;let s=-1;if(this.subscribers=Array.prototype.slice.call(this.el.children).filter(L).map((e,o)=>{const i=n(t.pathname,{path:e.url,exact:e.exact,strict:!0});return i&&-1===s&&(s=o),{el:e,match:i}}),-1===s)return;if(this.activeIndex===s)return void(this.subscribers[s].el.match=this.subscribers[s].match);this.activeIndex=s;const e=this.subscribers[this.activeIndex];this.scrollTopOffset&&(e.el.scrollTopOffset=this.scrollTopOffset),e.el.group=this.group,e.el.match=e.match,e.el.componentUpdated=t=>{this.queue.write(()=>{this.subscribers.forEach((t,s)=>{if(t.el.componentUpdated=void 0,s===this.activeIndex)return t.el.style.display="";this.scrollTopOffset&&(t.el.scrollTopOffset=this.scrollTopOffset),t.el.group=this.group,t.el.match=null,t.el.style.display="none"})}),this.routeViewsUpdated&&this.routeViewsUpdated(Object.assign({scrollTopOffset:this.scrollTopOffset},t))}}render(){return s("slot",null)}get el(){return e(this)}static get watchers(){return{location:["regenerateSubscribers"]}}}i.injectProps(S,["location","routeViewsUpdated"]);const U=(t,...s)=>{t||console.warn(...s)},k=()=>{let t,s=[];return{setPrompt:s=>(U(null==t,"A history supports only one prompt at a time"),t=s,()=>{t===s&&(t=null)}),confirmTransitionTo:(s,e,o,i)=>{if(null!=t){const n="function"==typeof t?t(s,e):t;"string"==typeof n?"function"==typeof o?o(n,i):(U(!1,"A history needs a getUserConfirmation function in order to use a prompt message"),i(!0)):i(!1!==n)}else i(!0)},appendListener:t=>{let e=!0;const o=(...s)=>{e&&t(...s)};return s.push(o),()=>{e=!1,s=s.filter(t=>t!==o)}},notifyListeners:(...t)=>{s.forEach(s=>s(...t))}}},H=(t,s="scrollPositions")=>{let e=new Map;const o=(s,o)=>{if(e.set(s,o),a(t,"sessionStorage")){const s=[];e.forEach((t,e)=>{s.push([e,t])}),t.sessionStorage.setItem("scrollPositions",JSON.stringify(s))}};if(a(t,"sessionStorage")){const o=t.sessionStorage.getItem(s);e=o?new Map(JSON.parse(o)):e}return"scrollRestoration"in t.history&&(history.scrollRestoration="manual"),{set:o,get:t=>e.get(t),has:t=>e.has(t),capture:s=>{o(s,[t.scrollX,t.scrollY])}}},T={hashbang:{encodePath:t=>"!"===t.charAt(0)?t:"!/"+P(t),decodePath:t=>"!"===t.charAt(0)?t.substr(1):t},noslash:{encodePath:P,decodePath:u},slash:{encodePath:u,decodePath:u}},E=(t,s)=>{const e=0==t.pathname.indexOf(s)?"/"+t.pathname.slice(s.length):t.pathname;return Object.assign({},t,{pathname:e})},A={browser:(t,s={})=>{let e=!1;const o=t.history,i=t.location,n=t.navigator,r=h(t),a=!c(n),w=H(t),P=null!=s.forceRefresh&&s.forceRefresh,v=null!=s.getUserConfirmation?s.getUserConfirmation:m,O=null!=s.keyLength?s.keyLength:6,j=s.basename?l(u(s.basename)):"",L=()=>{try{return t.history.state||{}}catch(t){return{}}},S=t=>{t=t||{};const{key:s,state:e}=t,{pathname:o,search:n,hash:r}=i;let a=o+n+r;return U(!j||f(a,j),'You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path "'+a+'" to begin with "'+j+'".'),j&&(a=g(a,j)),p(a,e,s||d(O))},T=k(),E=t=>{w.capture(q.location.key),Object.assign(q,t),q.location.scrollPosition=w.get(q.location.key),q.length=o.length,T.notifyListeners(q.location,q.action)},A=t=>{b(n,t)||C(S(t.state))},x=()=>{C(S(L()))},C=t=>{if(e)e=!1,E();else{const s="POP";T.confirmTransitionTo(t,s,v,e=>{e?E({action:s,location:t}):R(t)})}},R=t=>{let s=B.indexOf(q.location.key),o=B.indexOf(t.key);-1===s&&(s=0),-1===o&&(o=0);const i=s-o;i&&(e=!0,N(i))},M=S(L());let B=[M.key],I=0,_=!1;const Y=t=>j+y(t),N=t=>{o.go(t)},V=s=>{I+=s,1===I?(t.addEventListener("popstate",A),a&&t.addEventListener("hashchange",x)):0===I&&(t.removeEventListener("popstate",A),a&&t.removeEventListener("hashchange",x))},q={length:o.length,action:"POP",location:M,createHref:Y,push:(t,s)=>{U(!("object"==typeof t&&void 0!==t.state&&void 0!==s),"You should avoid providing a 2nd state argument to push when the 1st argument is a location-like object that already has state; it is ignored");const e=p(t,s,d(O),q.location);T.confirmTransitionTo(e,"PUSH",v,t=>{if(!t)return;const s=Y(e),{key:n,state:a}=e;if(r)if(o.pushState({key:n,state:a},"",s),P)i.href=s;else{const t=B.indexOf(q.location.key),s=B.slice(0,-1===t?0:t+1);s.push(e.key),B=s,E({action:"PUSH",location:e})}else U(void 0===a,"Browser history cannot push state in browsers that do not support HTML5 history"),i.href=s})},replace:(t,s)=>{U(!("object"==typeof t&&void 0!==t.state&&void 0!==s),"You should avoid providing a 2nd state argument to replace when the 1st argument is a location-like object that already has state; it is ignored");const e=p(t,s,d(O),q.location);T.confirmTransitionTo(e,"REPLACE",v,t=>{if(!t)return;const s=Y(e),{key:n,state:a}=e;if(r)if(o.replaceState({key:n,state:a},"",s),P)i.replace(s);else{const t=B.indexOf(q.location.key);-1!==t&&(B[t]=e.key),E({action:"REPLACE",location:e})}else U(void 0===a,"Browser history cannot replace state in browsers that do not support HTML5 history"),i.replace(s)})},go:N,goBack:()=>N(-1),goForward:()=>N(1),block:(t="")=>{const s=T.setPrompt(t);return _||(V(1),_=!0),()=>(_&&(_=!1,V(-1)),s())},listen:t=>{const s=T.appendListener(t);return V(1),()=>{V(-1),s()}},win:t};return q},hash:(t,s={})=>{let e=!1,o=null,i=0,n=!1;const r=t.location,a=t.history,h=w(t.navigator),c=null!=s.keyLength?s.keyLength:6,{getUserConfirmation:b=m,hashType:P="slash"}=s,O=s.basename?l(u(s.basename)):"",{encodePath:j,decodePath:L}=T[P],S=()=>{const t=r.href,s=t.indexOf("#");return-1===s?"":t.substring(s+1)},H=t=>{const s=r.href.indexOf("#");r.replace(r.href.slice(0,s>=0?s:0)+"#"+t)},E=()=>{let t=L(S());return U(!O||f(t,O),'You are attempting to use a basename on a page whose URL path does not begin with the basename. Expected path "'+t+'" to begin with "'+O+'".'),O&&(t=g(t,O)),p(t,void 0,d(c))},A=k(),x=t=>{Object.assign(q,t),q.length=a.length,A.notifyListeners(q.location,q.action)},C=()=>{const t=S(),s=j(t);if(t!==s)H(s);else{const t=E(),s=q.location;if(!e&&v(s,t))return;if(o===y(t))return;o=null,R(t)}},R=t=>{if(e)e=!1,x();else{const s="POP";A.confirmTransitionTo(t,s,b,e=>{e?x({action:s,location:t}):M(t)})}},M=t=>{let s=Y.lastIndexOf(y(q.location)),o=Y.lastIndexOf(y(t));-1===s&&(s=0),-1===o&&(o=0);const i=s-o;i&&(e=!0,N(i))},B=S(),I=j(B);B!==I&&H(I);const _=E();let Y=[y(_)];const N=t=>{U(h,"Hash history go(n) causes a full page reload in this browser"),a.go(t)},V=(t,s)=>{i+=s,1===i?t.addEventListener("hashchange",C):0===i&&t.removeEventListener("hashchange",C)},q={length:a.length,action:"POP",location:_,createHref:t=>"#"+j(O+y(t)),push:(t,s)=>{U(void 0===s,"Hash history cannot push state; it is ignored");const e=p(t,void 0,d(c),q.location);A.confirmTransitionTo(e,"PUSH",b,t=>{if(!t)return;const s=y(e),i=j(O+s);if(S()!==i){o=s,(t=>{r.hash=t})(i);const t=Y.lastIndexOf(y(q.location)),n=Y.slice(0,-1===t?0:t+1);n.push(s),Y=n,x({action:"PUSH",location:e})}else U(!1,"Hash history cannot PUSH the same path; a new entry will not be added to the history stack"),x()})},replace:(t,s)=>{U(void 0===s,"Hash history cannot replace state; it is ignored");const e=p(t,void 0,d(c),q.location);A.confirmTransitionTo(e,"REPLACE",b,t=>{if(!t)return;const s=y(e),i=j(O+s);S()!==i&&(o=s,H(i));const n=Y.indexOf(y(q.location));-1!==n&&(Y[n]=s),x({action:"REPLACE",location:e})})},go:N,goBack:()=>N(-1),goForward:()=>N(1),block:(s="")=>{const e=A.setPrompt(s);return n||(V(t,1),n=!0),()=>(n&&(n=!1,V(t,-1)),e())},listen:s=>{const e=A.appendListener(s);return V(t,1),()=>{V(t,-1),e()}},win:t};return q}};class x{constructor(s){t(this,s),this.root="/",this.historyType="browser",this.titleSuffix="",this.routeViewsUpdated=(t={})=>{if(this.history&&t.scrollToId&&"browser"===this.historyType){const s=this.history.win.document.getElementById(t.scrollToId);if(s)return s.scrollIntoView()}this.scrollTo(t.scrollTopOffset||this.scrollTopOffset)},this.isServer=o(this,"isServer"),this.queue=o(this,"queue")}componentWillLoad(){this.history=A[this.historyType](this.el.ownerDocument.defaultView),this.history.listen(t=>{t=E(t,this.root),this.location=t}),this.location=E(this.history.location,this.root)}scrollTo(t){const s=this.history;if(null!=t&&!this.isServer&&s)return"POP"===s.action&&Array.isArray(s.location.scrollPosition)?this.queue.write(()=>{s&&s.location&&Array.isArray(s.location.scrollPosition)&&s.win.scrollTo(s.location.scrollPosition[0],s.location.scrollPosition[1])}):this.queue.write(()=>{s.win.scrollTo(0,t)})}render(){if(this.location&&this.history)return s(i.Provider,{state:{historyType:this.historyType,location:this.location,titleSuffix:this.titleSuffix,root:this.root,history:this.history,routeViewsUpdated:this.routeViewsUpdated}},s("slot",null))}get el(){return e(this)}}export{O as app_root,j as stencil_route,S as stencil_route_switch,x as stencil_router}