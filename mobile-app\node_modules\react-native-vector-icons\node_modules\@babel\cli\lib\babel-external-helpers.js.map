{"version": 3, "names": ["commander", "data", "_interopRequireWildcard", "require", "_core", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "program", "collect", "value", "previousValue", "values", "split", "push", "option", "usage", "parse", "process", "argv", "opts", "console", "log", "buildExternalHelpers", "whitelist", "outputType"], "sources": ["../src/babel-external-helpers.ts"], "sourcesContent": ["import * as commander from \"commander\";\nimport { buildExternalHelpers } from \"@babel/core\";\n\nconst program = process.env.BABEL_8_BREAKING\n  ? commander.program\n  : commander.default.program;\n\nfunction collect(value: unknown, previousValue: Array<string>): Array<string> {\n  // If the user passed the option with no value, like \"babel-external-helpers --whitelist\", do nothing.\n  if (typeof value !== \"string\") return previousValue;\n\n  const values = value.split(\",\");\n\n  if (previousValue) {\n    previousValue.push(...values);\n    return previousValue;\n  }\n  return values;\n}\n\nprogram.option(\n  \"-l, --whitelist [whitelist]\",\n  \"Whitelist of helpers to ONLY include\",\n  collect,\n);\nprogram.option(\n  \"-t, --output-type [type]\",\n  \"Type of output (global|umd|var)\",\n  \"global\",\n);\n\nprogram.usage(\"[options]\");\nprogram.parse(process.argv);\nconst opts = program.opts();\n\nconsole.log(buildExternalHelpers(opts.whitelist, opts.outputType));\n"], "mappings": ";;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmD,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEnD,MAAMW,OAAO,GAETzB,SAAS,CAAD,CAAC,CAACW,OAAO,CAACc,OAAO;AAE7B,SAASC,OAAOA,CAACC,KAAc,EAAEC,aAA4B,EAAiB;EAE5E,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE,OAAOC,aAAa;EAEnD,MAAMC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;EAE/B,IAAIF,aAAa,EAAE;IACjBA,aAAa,CAACG,IAAI,CAAC,GAAGF,MAAM,CAAC;IAC7B,OAAOD,aAAa;EACtB;EACA,OAAOC,MAAM;AACf;AAEAJ,OAAO,CAACO,MAAM,CACZ,6BAA6B,EAC7B,sCAAsC,EACtCN,OACF,CAAC;AACDD,OAAO,CAACO,MAAM,CACZ,0BAA0B,EAC1B,iCAAiC,EACjC,QACF,CAAC;AAEDP,OAAO,CAACQ,KAAK,CAAC,WAAW,CAAC;AAC1BR,OAAO,CAACS,KAAK,CAACC,OAAO,CAACC,IAAI,CAAC;AAC3B,MAAMC,IAAI,GAAGZ,OAAO,CAACY,IAAI,CAAC,CAAC;AAE3BC,OAAO,CAACC,GAAG,CAAC,IAAAC,4BAAoB,EAACH,IAAI,CAACI,SAAS,EAAEJ,IAAI,CAACK,UAAU,CAAC,CAAC", "ignoreList": []}