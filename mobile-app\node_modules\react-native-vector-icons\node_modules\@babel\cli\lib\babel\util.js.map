{"version": 3, "names": ["_fsReaddirRecursive", "data", "require", "babel", "_path", "_fs", "watcher", "asyncGeneratorStep", "n", "t", "e", "r", "o", "a", "c", "i", "u", "value", "done", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "chmod", "src", "dest", "fs", "chmodSync", "statSync", "mode", "_", "console", "warn", "alphasort", "b", "localeCompare", "readdir", "dirname", "includeDotfiles", "filter", "readdirRecursive", "filename", "index", "currentDirectory", "stat", "path", "join", "isDirectory", "readdirForCompilable", "altExts", "isCompilableExtension", "exts", "DEFAULT_EXTENSIONS", "ext", "extname", "includes", "addSourceMappingUrl", "code", "loc", "basename", "hasDataSourcemap", "pos", "lastIndexOf", "length", "CALLER", "name", "transformRepl", "opts", "Object", "assign", "sourceMaps", "caller", "reject", "transform", "err", "result", "compile", "_x", "_x2", "_compile", "transformFile", "externalDependencies", "updateExternalDependencies", "deleteDir", "rmSync", "d", "p", "existsSync", "readdirSync", "for<PERSON>ach", "f", "lstatSync", "unlinkSync", "rmdirSync", "force", "recursive", "process", "on", "error", "exitCode", "withExtension", "newBasename", "debounce", "fn", "time", "timer", "debounced", "clearTimeout", "setTimeout", "flush"], "sources": ["../../src/babel/util.ts"], "sourcesContent": ["import readdirRecursive from \"fs-readdir-recursive\";\nimport * as babel from \"@babel/core\";\nimport path from \"node:path\";\nimport fs from \"node:fs\";\n\nimport * as watcher from \"./watcher.ts\";\n\nimport type { FileResult, InputOptions } from \"@babel/core\";\n\nexport function chmod(src: string, dest: string): void {\n  try {\n    fs.chmodSync(dest, fs.statSync(src).mode);\n  } catch (_) {\n    console.warn(`Cannot change permissions of ${dest}`);\n  }\n}\n\nexport function alphasort(a: string, b: string) {\n  return a.localeCompare(b, \"en\");\n}\n\ntype ReaddirFilter = (filename: string) => boolean;\n\nexport function readdir(\n  dirname: string,\n  includeDotfiles: boolean,\n  filter?: ReaddirFilter,\n): Array<string> {\n  if (process.env.BABEL_8_BREAKING) {\n    return (\n      fs\n        .readdirSync(dirname, { recursive: true, withFileTypes: true })\n        .filter(dirent => {\n          // exclude directory entries from readdir results\n          if (dirent.isDirectory()) return false;\n          const filename = dirent.name;\n          return (\n            (includeDotfiles || filename[0] !== \".\") &&\n            (!filter || filter(filename))\n          );\n        })\n        .map(dirent => path.join(dirent.parentPath, dirent.name))\n        // readdirSyncRecursive conducts BFS, sort the entries so we can match the DFS behaviour of fs-readdir-recursive\n        // https://github.com/nodejs/node/blob/d6b12f5b77e35c58a611d614cf0aac674ec2c3ed/lib/fs.js#L1421\n        .sort(alphasort)\n    );\n  } else {\n    return readdirRecursive(\n      \"\",\n      (filename, index, currentDirectory) => {\n        const stat = fs.statSync(path.join(currentDirectory, filename));\n\n        // ensure we recurse into .* folders\n        if (stat.isDirectory()) return true;\n\n        return (\n          (includeDotfiles || filename[0] !== \".\") &&\n          (!filter || filter(filename))\n        );\n      },\n      // @ts-expect-error improve @types/fs-readdir-recursive typings\n      [],\n      dirname,\n    );\n  }\n}\n\nexport function readdirForCompilable(\n  dirname: string,\n  includeDotfiles: boolean,\n  altExts?: Array<string>,\n): Array<string> {\n  return readdir(dirname, includeDotfiles, function (filename) {\n    return isCompilableExtension(filename, altExts);\n  });\n}\n\n/**\n * Test if a filename ends with a compilable extension.\n */\nexport function isCompilableExtension(\n  filename: string,\n  altExts?: readonly string[],\n): boolean {\n  const exts = altExts || babel.DEFAULT_EXTENSIONS;\n  const ext = path.extname(filename);\n  return exts.includes(ext);\n}\n\nexport function addSourceMappingUrl(code: string, loc: string): string {\n  return code + \"\\n//# sourceMappingURL=\" + path.basename(loc);\n}\n\nexport function hasDataSourcemap(code: string): boolean {\n  const pos = code.lastIndexOf(\"\\n\", code.length - 2);\n  return pos !== -1 && code.lastIndexOf(\"//# sourceMappingURL\") < pos;\n}\n\nconst CALLER = {\n  name: \"@babel/cli\",\n};\n\nexport function transformRepl(filename: string, code: string, opts: any) {\n  opts = {\n    ...opts,\n    sourceMaps: opts.sourceMaps === \"inline\" ? true : opts.sourceMaps,\n    caller: CALLER,\n    filename,\n  };\n\n  return new Promise<FileResult>((resolve, reject) => {\n    babel.transform(code, opts, (err, result) => {\n      if (err) reject(err);\n      else resolve(result);\n    });\n  });\n}\n\nexport async function compile(filename: string, opts: InputOptions) {\n  opts = {\n    ...opts,\n    caller: CALLER,\n  };\n\n  const result = process.env.BABEL_8_BREAKING\n    ? await babel.transformFileAsync(filename, opts)\n    : await new Promise<FileResult>((resolve, reject) => {\n        babel.transformFile(filename, opts, (err, result) => {\n          if (err) reject(err);\n          else resolve(result);\n        });\n      });\n\n  if (result) {\n    if (!process.env.BABEL_8_BREAKING) {\n      if (!result.externalDependencies) return result;\n    }\n    watcher.updateExternalDependencies(filename, result.externalDependencies);\n  }\n\n  return result;\n}\n\nexport function deleteDir(path: string): void {\n  fs.rmSync(path, { force: true, recursive: true });\n}\n\nprocess.on(\"uncaughtException\", function (err) {\n  console.error(err);\n  process.exitCode = 1;\n});\n\nexport function withExtension(filename: string, ext: string = \".js\") {\n  const newBasename = path.basename(filename, path.extname(filename)) + ext;\n  return path.join(path.dirname(filename), newBasename);\n}\n\nexport function debounce(fn: () => void, time: number) {\n  let timer: NodeJS.Timeout;\n  function debounced() {\n    clearTimeout(timer);\n    timer = setTimeout(fn, time);\n  }\n  debounced.flush = () => {\n    clearTimeout(timer);\n    fn();\n  };\n  return debounced;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SAAAA,oBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,mBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAK,OAAA,GAAAJ,OAAA;AAAwC,SAAAK,mBAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,cAAAC,CAAA,GAAAP,CAAA,CAAAK,CAAA,EAAAC,CAAA,GAAAE,CAAA,GAAAD,CAAA,CAAAE,KAAA,WAAAT,CAAA,gBAAAE,CAAA,CAAAF,CAAA,KAAAO,CAAA,CAAAG,IAAA,GAAAT,CAAA,CAAAO,CAAA,IAAAG,OAAA,CAAAC,OAAA,CAAAJ,CAAA,EAAAK,IAAA,CAAAV,CAAA,EAAAC,CAAA;AAAA,SAAAU,kBAAAd,CAAA,6BAAAC,CAAA,SAAAC,CAAA,GAAAa,SAAA,aAAAJ,OAAA,WAAAR,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAL,CAAA,CAAAgB,KAAA,CAAAf,CAAA,EAAAC,CAAA,YAAAe,MAAAjB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,UAAAlB,CAAA,cAAAkB,OAAAlB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,WAAAlB,CAAA,KAAAiB,KAAA;AAIjC,SAASE,KAAKA,CAACC,GAAW,EAAEC,IAAY,EAAQ;EACrD,IAAI;IACFC,IAACA,CAAC,CAACC,SAAS,CAACF,IAAI,EAAEC,IAACA,CAAC,CAACE,QAAQ,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC;EAC3C,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVC,OAAO,CAACC,IAAI,CAAC,gCAAgCP,IAAI,EAAE,CAAC;EACtD;AACF;AAEO,SAASQ,SAASA,CAACxB,CAAS,EAAEyB,CAAS,EAAE;EAC9C,OAAOzB,CAAC,CAAC0B,aAAa,CAACD,CAAC,EAAE,IAAI,CAAC;AACjC;AAIO,SAASE,OAAOA,CACrBC,OAAe,EACfC,eAAwB,EACxBC,MAAsB,EACP;EAmBR;IACL,OAAOC,oBAAeA,CAAC,CACrB,EAAE,EACF,CAACC,QAAQ,EAAEC,KAAK,EAAEC,gBAAgB,KAAK;MACrC,MAAMC,IAAI,GAAGlB,IAACA,CAAC,CAACE,QAAQ,CAACiB,MAAGA,CAAC,CAACC,IAAI,CAACH,gBAAgB,EAAEF,QAAQ,CAAC,CAAC;MAG/D,IAAIG,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE,OAAO,IAAI;MAEnC,OACE,CAACT,eAAe,IAAIG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,MACtC,CAACF,MAAM,IAAIA,MAAM,CAACE,QAAQ,CAAC,CAAC;IAEjC,CAAC,EAED,EAAE,EACFJ,OACF,CAAC;EACH;AACF;AAEO,SAASW,oBAAoBA,CAClCX,OAAe,EACfC,eAAwB,EACxBW,OAAuB,EACR;EACf,OAAOb,OAAO,CAACC,OAAO,EAAEC,eAAe,EAAE,UAAUG,QAAQ,EAAE;IAC3D,OAAOS,qBAAqB,CAACT,QAAQ,EAAEQ,OAAO,CAAC;EACjD,CAAC,CAAC;AACJ;AAKO,SAASC,qBAAqBA,CACnCT,QAAgB,EAChBQ,OAA2B,EAClB;EACT,MAAME,IAAI,GAAGF,OAAO,IAAIlD,KAAK,CAAD,CAAC,CAACqD,kBAAkB;EAChD,MAAMC,GAAG,GAAGR,MAAGA,CAAC,CAACS,OAAO,CAACb,QAAQ,CAAC;EAClC,OAAOU,IAAI,CAACI,QAAQ,CAACF,GAAG,CAAC;AAC3B;AAEO,SAASG,mBAAmBA,CAACC,IAAY,EAAEC,GAAW,EAAU;EACrE,OAAOD,IAAI,GAAG,yBAAyB,GAAGZ,MAAGA,CAAC,CAACc,QAAQ,CAACD,GAAG,CAAC;AAC9D;AAEO,SAASE,gBAAgBA,CAACH,IAAY,EAAW;EACtD,MAAMI,GAAG,GAAGJ,IAAI,CAACK,WAAW,CAAC,IAAI,EAAEL,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;EACnD,OAAOF,GAAG,KAAK,CAAC,CAAC,IAAIJ,IAAI,CAACK,WAAW,CAAC,sBAAsB,CAAC,GAAGD,GAAG;AACrE;AAEA,MAAMG,MAAM,GAAG;EACbC,IAAI,EAAE;AACR,CAAC;AAEM,SAASC,aAAaA,CAACzB,QAAgB,EAAEgB,IAAY,EAAEU,IAAS,EAAE;EACvEA,IAAI,GAAAC,MAAA,CAAAC,MAAA,KACCF,IAAI;IACPG,UAAU,EAAEH,IAAI,CAACG,UAAU,KAAK,QAAQ,GAAG,IAAI,GAAGH,IAAI,CAACG,UAAU;IACjEC,MAAM,EAAEP,MAAM;IACdvB;EAAQ,EACT;EAED,OAAO,IAAI1B,OAAO,CAAa,CAACC,OAAO,EAAEwD,MAAM,KAAK;IAClDzE,KAAK,CAAD,CAAC,CAAC0E,SAAS,CAAChB,IAAI,EAAEU,IAAI,EAAE,CAACO,GAAG,EAAEC,MAAM,KAAK;MAC3C,IAAID,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC,KAChB1D,OAAO,CAAC2D,MAAM,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAAC,SAEqBC,OAAOA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,QAAA,CAAA3D,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAA4D,SAAA;EAAAA,QAAA,GAAA7D,iBAAA,CAAtB,WAAuBuB,QAAgB,EAAE0B,IAAkB,EAAE;IAClEA,IAAI,GAAAC,MAAA,CAAAC,MAAA,KACCF,IAAI;MACPI,MAAM,EAAEP;IAAM,EACf;IAED,MAAMW,MAAM,SAEF,IAAI5D,OAAO,CAAa,CAACC,OAAO,EAAEwD,MAAM,KAAK;MACjDzE,KAAK,CAAD,CAAC,CAACiF,aAAa,CAACvC,QAAQ,EAAE0B,IAAI,EAAE,CAACO,GAAG,EAAEC,MAAM,KAAK;QACnD,IAAID,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC,KAChB1D,OAAO,CAAC2D,MAAM,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEN,IAAIA,MAAM,EAAE;MACyB;QACjC,IAAI,CAACA,MAAM,CAACM,oBAAoB,EAAE,OAAON,MAAM;MACjD;MACAzE,OAAO,CAACgF,0BAA0B,CAACzC,QAAQ,EAAEkC,MAAM,CAACM,oBAAoB,CAAC;IAC3E;IAEA,OAAON,MAAM;EACf,CAAC;EAAA,OAAAI,QAAA,CAAA3D,KAAA,OAAAD,SAAA;AAAA;AAEM,SAASgE,SAASA,CAACtC,IAAY,EAAQ;EAC5C,CAAA5C,GAAA,GAAAmF,MAAA,aAAAC,EAAAC,CAAA;IAAA,IAAArF,GAAA,GAAAsF,UAAA,CAAAD,CAAA;MAAArF,GAAA,GAAAuF,WAAA,CAAAF,CAAA,EAAAG,OAAA,WAAAC,CAAA;QAAA,MAAAhF,CAAA,GAAA4E,CAAA,SAAAI,CAAA;QAAA,IAAAzF,GAAA,GAAA0F,SAAA,CAAAjF,CAAA,EAAAqC,WAAA;UAAAsC,CAAA,CAAA3E,CAAA;QAAA;UAAAT,GAAA,GAAA2F,UAAA,CAAAlF,CAAA;QAAA;MAAA;MAAAT,GAAA,GAAA4F,SAAA,CAAAP,CAAA;IAAA;EAAA,GAAUzC,IAAI,EAAE;IAAEiD,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAK,CAAC,CAAC;AACnD;AAEAC,OAAO,CAACC,EAAE,CAAC,mBAAmB,EAAE,UAAUvB,GAAG,EAAE;EAC7C3C,OAAO,CAACmE,KAAK,CAACxB,GAAG,CAAC;EAClBsB,OAAO,CAACG,QAAQ,GAAG,CAAC;AACtB,CAAC,CAAC;AAEK,SAASC,aAAaA,CAAC3D,QAAgB,EAAEY,GAAW,GAAG,KAAK,EAAE;EACnE,MAAMgD,WAAW,GAAGxD,MAAGA,CAAC,CAACc,QAAQ,CAAClB,QAAQ,EAAEI,MAAGA,CAAC,CAACS,OAAO,CAACb,QAAQ,CAAC,CAAC,GAAGY,GAAG;EACzE,OAAOR,MAAGA,CAAC,CAACC,IAAI,CAACD,MAAGA,CAAC,CAACR,OAAO,CAACI,QAAQ,CAAC,EAAE4D,WAAW,CAAC;AACvD;AAEO,SAASC,QAAQA,CAACC,EAAc,EAAEC,IAAY,EAAE;EACrD,IAAIC,KAAqB;EACzB,SAASC,SAASA,CAAA,EAAG;IACnBC,YAAY,CAACF,KAAK,CAAC;IACnBA,KAAK,GAAGG,UAAU,CAACL,EAAE,EAAEC,IAAI,CAAC;EAC9B;EACAE,SAAS,CAACG,KAAK,GAAG,MAAM;IACtBF,YAAY,CAACF,KAAK,CAAC;IACnBF,EAAE,CAAC,CAAC;EACN,CAAC;EACD,OAAOG,SAAS;AAClB", "ignoreList": []}