import{r as t,d as s,h as i,g as e}from"./p-fbbae598.js";import{s as n}from"./p-081b0641.js";const h={threshold:.1,includeAA:!1,alpha:.1,aaColor:[255,255,0],diffColor:[255,0,0],diffColorAlt:null,diffMask:!1};function o(t){return ArrayBuffer.isView(t)&&1===t.constructor.BYTES_PER_ELEMENT}function r(t,s,i,e,n,h){const o=Math.max(s-1,0),r=Math.max(i-1,0),c=Math.min(s+1,e-1),d=Math.min(i+1,n-1),f=4*(i*e+s);let u,p,m,g,w=s===o||s===c||i===r||i===d?1:0,y=0,v=0;for(let n=o;n<=c;n++)for(let h=r;h<=d;h++){if(n===s&&h===i)continue;const o=l(t,t,f,4*(h*e+n),!0);if(0===o){if(w++,w>2)return!1}else o<y?(y=o,u=n,p=h):o>v&&(v=o,m=n,g=h)}return 0!==y&&0!==v&&(a(t,u,p,e,n)&&a(h,u,p,e,n)||a(t,m,g,e,n)&&a(h,m,g,e,n))}function a(t,s,i,e,n){const h=Math.max(s-1,0),o=Math.max(i-1,0),r=Math.min(s+1,e-1),a=Math.min(i+1,n-1),l=4*(i*e+s);let c=s===h||s===r||i===o||i===a?1:0;for(let n=h;n<=r;n++)for(let h=o;h<=a;h++){if(n===s&&h===i)continue;const o=4*(h*e+n);if(t[l]===t[o]&&t[l+1]===t[o+1]&&t[l+2]===t[o+2]&&t[l+3]===t[o+3]&&c++,c>2)return!0}return!1}function l(t,s,i,e,n){let h=t[i+0],o=t[i+1],r=t[i+2],a=t[i+3],l=s[e+0],p=s[e+1],m=s[e+2],g=s[e+3];if(a===g&&h===l&&o===p&&r===m)return 0;a<255&&(a/=255,h=u(h,a),o=u(o,a),r=u(r,a)),g<255&&(g/=255,l=u(l,g),p=u(p,g),m=u(m,g));const w=c(h,o,r),y=c(l,p,m),v=w-y;if(n)return v;const b=d(h,o,r)-d(l,p,m),x=f(h,o,r)-f(l,p,m),M=.5053*v*v+.299*b*b+.1957*x*x;return w>y?-M:M}function c(t,s,i){return.29889531*t+.58662247*s+.11448223*i}function d(t,s,i){return.59597799*t-.2741761*s-.32180189*i}function f(t,s,i){return.21147017*t-.52261711*s+.31114694*i}function u(t,s){return 255+(t-255)*s}function p(t,s,i,e,n){t[s+0]=i,t[s+1]=e,t[s+2]=n,t[s+3]=255}function m(t,s,i,e){const n=u(c(t[s+0],t[s+1],t[s+2]),i*t[s+3]/255);p(e,s,n,n,n)}function g(t,s,i){if(y.has(s))return void i(y.get(s));if(w.has(s))return void w.get(s).push(i);w.set(s,[i]);const e=document.createElement("script");e.src=`${t}screenshot_${s}.js`,document.head.appendChild(e)}window.loadScreenshot=(t,s)=>{const i=w.get(t);i&&(i.forEach(t=>t(s)),w.delete(t)),y.set(t,s)};const w=new Map,y=new Map;class v{constructor(i){t(this,i),this.imageASrc=null,this.imageBSrc=null,this.imageAClass="is-loading",this.imageBClass="is-loading",this.canvasClass="is-loading",this.imagesLoaded=new Set,this.isImageALoaded=!1,this.isImageBLoaded=!1,this.isMismatchInitialized=!1,this.hasCalculatedMismatch=!1,this.compareLoaded=s(this,"compareLoaded",7)}componentWillLoad(){this.loadScreenshots()}componentWillUpdate(){this.loadScreenshots()}loadScreenshots(){if(this.show&&this.diff.hasIntersected)return this.diff.identical?(this.imageASrc=this.imagesUrl+this.diff.imageA,this.isImageALoaded=!0,this.imageAClass="has-loaded",this.imageBSrc=this.imagesUrl+this.diff.imageB,this.isImageBLoaded=!0,void(this.imageBClass="has-loaded")):void(this.isMismatchInitialized||(this.isMismatchInitialized=!0,null!=this.jsonpUrl?(null!=this.diff.imageA&&g(this.jsonpUrl,this.diff.imageA,t=>{this.imageASrc=t}),null!=this.diff.imageB&&g(this.jsonpUrl,this.diff.imageB,t=>{this.imageBSrc=t})):(this.imageASrc=this.imagesUrl+this.diff.imageA,this.imageBSrc=this.imagesUrl+this.diff.imageB)))}async compareImages(){const t=this.diff;this.isImageALoaded&&this.isImageBLoaded&&!this.hasCalculatedMismatch&&t.comparable&&(this.hasCalculatedMismatch=!0,t.mismatchedPixels=await function(t,s,i,e,n,a){let c=-1;try{const d=document.createElement("canvas");d.width=e,d.height=n;const f=document.createElement("canvas");f.width=e,f.height=n;const u=d.getContext("2d");u.drawImage(t,0,0);const g=f.getContext("2d");g.drawImage(s,0,0);const w=document.createElement("canvas").getContext("2d");w.drawImage(t,0,0),w.getImageData(0,0,e,n);const y=u.getImageData(0,0,e,n).data,v=g.getImageData(0,0,e,n).data,b=i.getContext("2d"),x=b.createImageData(e,d.height);c=function(t,s,i,e,n,a){if(!o(t)||!o(s)||i&&!o(i))throw new Error("Image data: Uint8Array, Uint8ClampedArray or Buffer expected.");if(t.length!==s.length||i&&i.length!==t.length)throw new Error("Image sizes do not match.");if(t.length!==e*n*4)throw new Error("Image data size does not match width/height.");a=Object.assign({},h,a);const c=e*n,d=new Uint32Array(t.buffer,t.byteOffset,c),f=new Uint32Array(s.buffer,s.byteOffset,c);let u=!0;for(let t=0;t<c;t++)if(d[t]!==f[t]){u=!1;break}if(u){if(i&&!a.diffMask)for(let s=0;s<c;s++)m(t,4*s,a.alpha,i);return 0}const g=35215*a.threshold*a.threshold;let w=0;for(let h=0;h<n;h++)for(let o=0;o<e;o++){const c=4*(h*e+o),d=l(t,s,c,c);Math.abs(d)>g?a.includeAA||!r(t,o,h,e,n,s)&&!r(s,o,h,e,n,t)?(i&&p(i,c,...d<0&&a.diffColorAlt||a.diffColor),w++):i&&!a.diffMask&&p(i,c,...a.aaColor):i&&(a.diffMask||m(t,c,a.alpha,i))}return w}(y,v,x.data,e,n,{threshold:a}),b.putImageData(x,0,0)}catch(t){console.error(t)}return c}(this.imageA,this.imageB,this.canvas,Math.round(t.width*t.deviceScaleFactor),Math.round(t.height*t.deviceScaleFactor),t.threshold),this.canvasClass="has-loaded",n(t.imageA,t.imageB,t.mismatchedPixels,t.threshold),this.compareLoaded.emit(t))}render(){const t=this.diff,s={width:t.width+"px",height:t.height+"px"};return[i("compare-cell",null,null!=t.imageA?i("a",{href:this.imagesUrl+t.imageA,target:"_blank"},i("img",{src:this.imageASrc,class:this.imageAClass,style:s,onLoad:this.diff.identical?null:()=>{this.isImageALoaded=!0,this.imageAClass="has-loaded",this.compareImages()},ref:t=>this.imageA=t})):i("img",{style:s,class:"is-loading"})),i("compare-cell",null,null!=t.imageB?i("a",{href:this.imagesUrl+t.imageB,target:"_blank"},i("img",{src:this.imageBSrc,class:this.imageBClass,style:s,onLoad:this.diff.identical?null:()=>{this.isImageBLoaded=!0,this.imageBClass="has-loaded",this.compareImages()},ref:t=>this.imageB=t})):i("img",{style:s,class:"is-loading"})),i("compare-cell",null,this.diff.identical?i("img",{style:s,src:this.imageASrc}):i("canvas",{width:Math.round(t.width*t.deviceScaleFactor),height:Math.round(t.height*t.deviceScaleFactor),class:this.canvasClass,style:s,hidden:!t.comparable,ref:t=>this.canvas=t})),i("compare-cell",null,i("compare-analysis",{aId:this.aId,bId:this.bId,mismatchedPixels:this.diff.mismatchedPixels,diff:this.diff}))]}get elm(){return e(this)}}v.style="compare-row img,compare-row canvas{display:block;box-shadow:var(--screenshot-box-shadow);border-radius:var(--screenshot-border-radius)}compare-row a{display:block}.is-loading{visibility:hidden}";class b{constructor(s){t(this,s)}render(){if(!this.a||!this.b||!this.diffs)return;let t=0;this.diffs.forEach(s=>{s.width>t&&(t=s.width)}),t-=6;const s={width:t+"px"};return[i("th-cell",null,i("div",{style:s},i("a",{href:this.a.url,target:"_blank"},this.a.message))),i("th-cell",null,i("div",{style:s},i("a",{href:this.b.url,target:"_blank"},this.b.message))),i("th-cell",null,i("div",{style:s},i("a",{href:`https://github.com/ionic-team/ionic/compare/${this.a.id}...${this.b.id}`,target:"_blank"},"Compare: ",this.a.id," - ",this.b.id))),i("th-cell",{class:"analysis"},i("div",null,"Analysis"))]}}b.style=":host{display:flex}th-cell{display:block;flex:1;font-weight:500;font-size:12px}th-cell div{padding-left:12px;padding-right:12px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}th-cell a{color:var(--font-color);text-decoration:none}th-cell a:hover{color:var(--analysis-data-color);text-decoration:underline}.analysis div{width:262px}";export{v as compare_row,b as compare_thead}