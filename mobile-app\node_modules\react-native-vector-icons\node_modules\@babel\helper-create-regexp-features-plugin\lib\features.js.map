{"version": 3, "names": ["FEATURES", "exports", "Object", "freeze", "unicodeFlag", "dotAllFlag", "unicodePropertyEscape", "namedCaptureGroups", "unicodeSetsFlag_syntax", "unicodeSetsFlag", "duplicateNamedCaptureGroups", "modifiers", "<PERSON><PERSON><PERSON>", "runtimeKey", "enableFeature", "features", "feature", "hasFeature"], "sources": ["../src/features.ts"], "sourcesContent": ["export const FEATURES = Object.freeze({\n  unicodeFlag: 1 << 0,\n  dotAllFlag: 1 << 1,\n  unicodePropertyEscape: 1 << 2,\n  namedCaptureGroups: 1 << 3,\n  // Not used, for backward compatibility with syntax-unicode-sets-regex\n  unicodeSetsFlag_syntax: 1 << 4,\n  unicodeSetsFlag: 1 << 5,\n  duplicateNamedCaptureGroups: 1 << 6,\n  modifiers: 1 << 7,\n});\n\n// We can't use a symbol because this needs to always be the same, even if\n// this package isn't deduped by npm. e.g.\n//  - node_modules/\n//    - @babel/plugin-regexp-features\n//    - @babel/plugin-transform-unicode-property-regex\n//      - node_modules\n//        - @babel-plugin-regexp-features\nexport const featuresKey = \"@babel/plugin-regexp-features/featuresKey\";\nexport const runtimeKey = \"@babel/plugin-regexp-features/runtimeKey\";\n\ntype FeatureType = (typeof FEATURES)[keyof typeof FEATURES];\n\nexport function enableFeature(features: number, feature: FeatureType): number {\n  return features | feature;\n}\n\nexport function hasFeature(features: number, feature: FeatureType) {\n  return !!(features & feature);\n}\n"], "mappings": ";;;;;;;;;;AAAO,MAAMA,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAGE,MAAM,CAACC,MAAM,CAAC;EACpCC,WAAW,EAAE,CAAC,IAAI,CAAC;EACnBC,UAAU,EAAE,CAAC,IAAI,CAAC;EAClBC,qBAAqB,EAAE,CAAC,IAAI,CAAC;EAC7BC,kBAAkB,EAAE,CAAC,IAAI,CAAC;EAE1BC,sBAAsB,EAAE,CAAC,IAAI,CAAC;EAC9BC,eAAe,EAAE,CAAC,IAAI,CAAC;EACvBC,2BAA2B,EAAE,CAAC,IAAI,CAAC;EACnCC,SAAS,EAAE,CAAC,IAAI;AAClB,CAAC,CAAC;AASK,MAAMC,WAAW,GAAAX,OAAA,CAAAW,WAAA,GAAG,2CAA2C;AAC/D,MAAMC,UAAU,GAAAZ,OAAA,CAAAY,UAAA,GAAG,0CAA0C;AAI7D,SAASC,aAAaA,CAACC,QAAgB,EAAEC,OAAoB,EAAU;EAC5E,OAAOD,QAAQ,GAAGC,OAAO;AAC3B;AAEO,SAASC,UAAUA,CAACF,QAAgB,EAAEC,OAAoB,EAAE;EACjE,OAAO,CAAC,EAAED,QAAQ,GAAGC,OAAO,CAAC;AAC/B", "ignoreList": []}