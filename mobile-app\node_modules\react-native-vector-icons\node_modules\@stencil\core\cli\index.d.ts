import type { CliInitOptions, CompilerSystem, Config, ConfigFlags, Logger, TaskCommand } from '../internal/index';
/**
 * Runs the CLI with the given options. This is used by Stencil's default `bin/stencil` file,
 * but can be used externally too.
 */
export declare function run(init: CliInitOptions): Promise<void>;
/**
 * Run individual CLI tasks.
 * @param coreCompiler The core Stencil compiler to be used. The `run()` method handles loading the core compiler, however, `runTask()` must be passed it.
 * @param config Assumes the config has already been validated and has the "sys" and "logger" properties.
 * @param task The task command to run, such as `build`.
 */
export declare function runTask(coreCompiler: any, config: Config, task: TaskCommand): Promise<void>;
export declare function parseFlags(args: string[], sys?: CompilerSystem): ConfigFlags;
export { CompilerSystem, Config, ConfigFlags, Logger, TaskCommand };
