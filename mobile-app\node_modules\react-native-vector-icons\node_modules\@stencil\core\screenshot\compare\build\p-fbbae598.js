let e,t,n,l=!1,o=!1,s=!1,r=0,i=!1;const c="undefined"!=typeof window?window:{},a=c.document||{head:{}},f={t:0,l:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,n,l)=>e.addEventListener(t,n,l),rel:(e,t,n,l)=>e.removeEventListener(t,n,l)},u=e=>Promise.resolve(e),d=(()=>{try{return new CSSStyleSheet,!0}catch(e){}return!1})(),p={},$=(e,t,n)=>{n&&n.map(([n,l,o])=>{const s=e,r=m(t,o),i=h(n);f.ael(s,l,r,i),(t.o=t.o||[]).push(()=>f.rel(s,l,r,i))})},m=(e,t)=>n=>{256&e.t?e.s[t](n):(e.i=e.i||[]).push([t,n])},h=e=>0!=(2&e),y="http://www.w3.org/1999/xlink",b=new WeakMap,w=e=>"sc-"+e.u,k={},v=e=>"object"==(e=typeof e)||"function"===e,g=(e,t,...n)=>{let l=null,o=null,s=null,r=!1,i=!1,c=[];const a=t=>{for(let n=0;n<t.length;n++)l=t[n],Array.isArray(l)?a(l):null!=l&&"boolean"!=typeof l&&((r="function"!=typeof e&&!v(l))&&(l+=""),r&&i?c[c.length-1].p+=l:c.push(r?S(null,l):l),i=r)};if(a(n),t){t.key&&(o=t.key),t.name&&(s=t.name);{const e=t.className||t.class;e&&(t.class="object"!=typeof e?e:Object.keys(e).filter(t=>e[t]).join(" "))}}if("function"==typeof e)return e(null===t?{}:t,c,M);const f=S(e,null);return f.$=t,c.length>0&&(f.m=c),f.h=o,f.k=s,f},S=(e,t)=>({t:0,v:e,p:t,g:null,m:null,$:null,h:null,k:null}),j={},M={forEach:(e,t)=>e.map(U).forEach(t),map:(e,t)=>e.map(U).map(t).map(C)},U=e=>({vattrs:e.$,vchildren:e.m,vkey:e.h,vname:e.k,vtag:e.v,vtext:e.p}),C=e=>{const t=S(e.vtag,e.vtext);return t.$=e.vattrs,t.m=e.vchildren,t.h=e.vkey,t.k=e.vname,t},R=(e,t,n,l,o,s)=>{if(n!==l){let r=de(e,t),i=t.toLowerCase();if("class"===t){const t=e.classList,o=L(n),s=L(l);t.remove(...o.filter(e=>e&&!s.includes(e))),t.add(...s.filter(e=>e&&!o.includes(e)))}else if("style"===t){for(const t in n)l&&null!=l[t]||(t.includes("-")?e.style.removeProperty(t):e.style[t]="");for(const t in l)n&&l[t]===n[t]||(t.includes("-")?e.style.setProperty(t,l[t]):e.style[t]=l[t])}else if("key"===t);else if("ref"===t)l&&l(e);else if(r||"o"!==t[0]||"n"!==t[1]){const c=v(l);if((r||c&&null!==l)&&!o)try{if(e.tagName.includes("-"))e[t]=l;else{let o=null==l?"":l;"list"===t?r=!1:null!=n&&e[t]==o||(e[t]=o)}}catch(e){}let a=!1;i!==(i=i.replace(/^xlink\:?/,""))&&(t=i,a=!0),null==l||!1===l?a?e.removeAttributeNS(y,t):e.removeAttribute(t):(!r||4&s||o)&&!c&&(l=!0===l?"":l,a?e.setAttributeNS(y,t,l):e.setAttribute(t,l))}else t="-"===t[2]?t.slice(3):de(c,i)?i.slice(2):i[2]+t.slice(3),n&&f.rel(e,t,n,!1),l&&f.ael(e,t,l,!1)}},x=/\s/,L=e=>e?e.split(x):[],O=(e,t,n,l)=>{const o=11===t.g.nodeType&&t.g.host?t.g.host:t.g,s=e&&e.$||k,r=t.$||k;for(l in s)l in r||R(o,l,s[l],void 0,n,t.t);for(l in r)R(o,l,s[l],r[l],n,t.t)},P=(o,r,i,c)=>{let f,u,d,p=r.m[i],$=0;if(l||(s=!0,"slot"===p.v&&(e&&c.classList.add(e+"-s"),p.t|=p.m?2:1)),null!==p.p)f=p.g=a.createTextNode(p.p);else if(1&p.t)f=p.g=a.createTextNode("");else if(f=p.g=a.createElement(2&p.t?"slot-fb":p.v),O(null,p,!1),null!=e&&f["s-si"]!==e&&f.classList.add(f["s-si"]=e),p.m)for($=0;$<p.m.length;++$)u=P(o,p,$,f),u&&f.appendChild(u);return f["s-hn"]=n,3&p.t&&(f["s-sr"]=!0,f["s-cr"]=t,f["s-sn"]=p.k||"",d=o&&o.m&&o.m[i],d&&d.v===p.v&&o.g&&T(o.g,!1)),f},T=(e,t)=>{f.t|=1;const l=e.childNodes;for(let e=l.length-1;e>=0;e--){const o=l[e];o["s-hn"]!==n&&o["s-ol"]&&(A(o).insertBefore(o,q(o)),o["s-ol"].remove(),o["s-ol"]=void 0,s=!0),t&&T(o,t)}f.t&=-2},E=(e,t,l,o,s,r)=>{let i,c=e["s-cr"]&&e["s-cr"].parentNode||e;for(c.shadowRoot&&c.tagName===n&&(c=c.shadowRoot);s<=r;++s)o[s]&&(i=P(null,l,s,e),i&&(o[s].g=i,c.insertBefore(i,q(t))))},W=(e,t,n,l,s)=>{for(;t<=n;++t)(l=e[t])&&(s=l.g,z(l),o=!0,s["s-ol"]?s["s-ol"].remove():T(s,!0),s.remove())},D=(e,t)=>e.v===t.v&&("slot"===e.v?e.k===t.k:e.h===t.h),q=e=>e&&e["s-ol"]||e,A=e=>(e["s-ol"]?e["s-ol"]:e).parentNode,F=(e,t)=>{const n=t.g=e.g,l=e.m,o=t.m,s=t.p;let r;null===s?("slot"===t.v||O(e,t,!1),null!==l&&null!==o?((e,t,n,l)=>{let o,s,r=0,i=0,c=0,a=0,f=t.length-1,u=t[0],d=t[f],p=l.length-1,$=l[0],m=l[p];for(;r<=f&&i<=p;)if(null==u)u=t[++r];else if(null==d)d=t[--f];else if(null==$)$=l[++i];else if(null==m)m=l[--p];else if(D(u,$))F(u,$),u=t[++r],$=l[++i];else if(D(d,m))F(d,m),d=t[--f],m=l[--p];else if(D(u,m))"slot"!==u.v&&"slot"!==m.v||T(u.g.parentNode,!1),F(u,m),e.insertBefore(u.g,d.g.nextSibling),u=t[++r],m=l[--p];else if(D(d,$))"slot"!==u.v&&"slot"!==m.v||T(d.g.parentNode,!1),F(d,$),e.insertBefore(d.g,u.g),d=t[--f],$=l[++i];else{for(c=-1,a=r;a<=f;++a)if(t[a]&&null!==t[a].h&&t[a].h===$.h){c=a;break}c>=0?(s=t[c],s.v!==$.v?o=P(t&&t[i],n,c,e):(F(s,$),t[c]=void 0,o=s.g),$=l[++i]):(o=P(t&&t[i],n,i,e),$=l[++i]),o&&A(u.g).insertBefore(o,q(u.g))}r>f?E(e,null==l[p+1]?null:l[p+1].g,n,l,i,p):i>p&&W(t,r,f)})(n,l,t,o):null!==o?(null!==e.p&&(n.textContent=""),E(n,null,t,o,0,o.length-1)):null!==l&&W(l,0,l.length-1)):(r=n["s-cr"])?r.parentNode.textContent=s:e.p!==s&&(n.data=s)},N=e=>{let t,n,l,o,s,r,i=e.childNodes;for(n=0,l=i.length;n<l;n++)if(t=i[n],1===t.nodeType){if(t["s-sr"])for(s=t["s-sn"],t.hidden=!1,o=0;o<l;o++)if(i[o]["s-hn"]!==t["s-hn"])if(r=i[o].nodeType,""!==s){if(1===r&&s===i[o].getAttribute("slot")){t.hidden=!0;break}}else if(1===r||3===r&&""!==i[o].textContent.trim()){t.hidden=!0;break}N(t)}},H=[],V=e=>{let t,n,l,s,r,i,c=0,a=e.childNodes,f=a.length;for(;c<f;c++){if(t=a[c],t["s-sr"]&&(n=t["s-cr"]))for(l=n.parentNode.childNodes,s=t["s-sn"],i=l.length-1;i>=0;i--)n=l[i],n["s-cn"]||n["s-nr"]||n["s-hn"]===t["s-hn"]||(_(n,s)?(r=H.find(e=>e.S===n),o=!0,n["s-sn"]=n["s-sn"]||s,r?r.j=t:H.push({j:t,S:n}),n["s-sr"]&&H.map(e=>{_(e.S,n["s-sn"])&&(r=H.find(e=>e.S===n),r&&!e.j&&(e.j=r.j))})):H.some(e=>e.S===n)||H.push({S:n}));1===t.nodeType&&V(t)}},_=(e,t)=>1===e.nodeType?null===e.getAttribute("slot")&&""===t||e.getAttribute("slot")===t:e["s-sn"]===t||""===t,z=e=>{e.$&&e.$.ref&&e.$.ref(null),e.m&&e.m.map(z)},B=e=>ae(e).M,G=(e,t,n)=>{const l=B(e);return{emit:e=>I(l,t,{bubbles:!!(4&n),composed:!!(2&n),cancelable:!!(1&n),detail:e})}},I=(e,t,n)=>{const l=new CustomEvent(t,n);return e.dispatchEvent(l),l},J=(e,t)=>{t&&!e.U&&t["s-p"]&&t["s-p"].push(new Promise(t=>e.U=t))},K=(e,t)=>{if(e.t|=16,!(4&e.t))return J(e,e.C),Me(()=>Q(e,t));e.t|=512},Q=(e,t)=>{const n=e.s;let l;return t?(e.t|=256,e.i&&(e.i.map(([e,t])=>te(n,e,t)),e.i=null),l=te(n,"componentWillLoad")):l=te(n,"componentWillUpdate"),ne(l,()=>X(e,n,t))},X=(r,i,c)=>{const u=r.M,d=u["s-rc"];c&&(e=>{const t=e.R,n=e.M,l=t.t,o=((e,t)=>{let n=w(t),l=he.get(n);if(e=11===e.nodeType?e:a,l)if("string"==typeof l){let t,o=b.get(e=e.head||e);o||b.set(e,o=new Set),o.has(n)||(t=a.createElement("style"),t.innerHTML=l,e.insertBefore(t,e.querySelector("link")),o&&o.add(n))}else e.adoptedStyleSheets.includes(l)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,l]);return n})(n.shadowRoot?n.shadowRoot:n.getRootNode(),t);10&l&&(n["s-sc"]=o,n.classList.add(o+"-h"))})(r),((r,i)=>{const c=r.M,u=r.R,d=r.L||S(null,null),p=(e=>e&&e.v===j)(i)?i:g(null,null,i);if(n=c.tagName,u.O&&(p.$=p.$||{},u.O.map(([e,t])=>p.$[t]=c[e])),p.v=null,p.t|=4,r.L=p,p.g=d.g=c.shadowRoot||c,e=c["s-sc"],t=c["s-cr"],l=0!=(1&u.t),o=!1,F(d,p),f.t|=1,s){let e,t,n,l,o,s;V(p.g);let r=0;for(;r<H.length;r++)e=H[r],t=e.S,t["s-ol"]||(n=a.createTextNode(""),n["s-nr"]=t,t.parentNode.insertBefore(t["s-ol"]=n,t));for(r=0;r<H.length;r++)if(e=H[r],t=e.S,e.j){for(l=e.j.parentNode,o=e.j.nextSibling,n=t["s-ol"];n=n.previousSibling;)if(s=n["s-nr"],s&&s["s-sn"]===t["s-sn"]&&l===s.parentNode&&(s=s.nextSibling,!s||!s["s-nr"])){o=s;break}(!o&&l!==t.parentNode||t.nextSibling!==o)&&t!==o&&(!t["s-hn"]&&t["s-ol"]&&(t["s-hn"]=t["s-ol"].parentNode.nodeName),l.insertBefore(t,o))}else 1===t.nodeType&&(t.hidden=!0)}o&&N(p.g),f.t&=-2,H.length=0})(r,Y(r,i)),d&&(d.map(e=>e()),u["s-rc"]=void 0);{const e=u["s-p"],t=()=>Z(r);0===e.length?t():(Promise.all(e).then(t),r.t|=4,e.length=0)}},Y=(e,t)=>{try{t=t.render&&t.render(),e.t&=-17,e.t|=2}catch(e){pe(e)}return t},Z=e=>{const t=e.M,n=e.s,l=e.C;64&e.t?te(n,"componentDidUpdate"):(e.t|=64,le(t),te(n,"componentDidLoad"),e.P(t),l||ee()),e.U&&(e.U(),e.U=void 0),512&e.t&&Se(()=>K(e,!1)),e.t&=-517},ee=()=>{le(a.documentElement),f.t|=2,Se(()=>I(c,"appload",{detail:{namespace:"app"}}))},te=(e,t,n)=>{if(e&&e[t])try{return e[t](n)}catch(e){pe(e)}},ne=(e,t)=>e&&e.then?e.then(t):t(),le=e=>e.classList.add("hydrated"),oe=(e,t,n)=>{if(t.T){e.watchers&&(t.W=e.watchers);const l=Object.entries(t.T),o=e.prototype;if(l.map(([e,[l]])=>{(31&l||2&n&&32&l)&&Object.defineProperty(o,e,{get(){return((e,t)=>ae(this).D.get(t))(0,e)},set(n){((e,t,n,l)=>{const o=ae(this),s=o.D.get(t),r=o.t,i=o.s;if(n=((e,t)=>null==e||v(e)?e:4&t?"false"!==e&&(""===e||!!e):2&t?parseFloat(e):1&t?e+"":e)(n,l.T[t][0]),!(8&r&&void 0!==s||n===s)&&(o.D.set(t,n),i)){if(l.W&&128&r){const e=l.W[t];e&&e.map(e=>{try{i[e](n,s,t)}catch(e){pe(e)}})}2==(18&r)&&K(o,!1)}})(0,e,n,t)},configurable:!0,enumerable:!0})}),1&n){const n=new Map;o.attributeChangedCallback=function(e,t,l){f.jmp(()=>{const t=n.get(e);this[t]=(null!==l||"boolean"!=typeof this[t])&&l})},e.observedAttributes=l.filter(([e,t])=>15&t[0]).map(([e,l])=>{const o=l[1]||e;return n.set(o,e),512&l[0]&&t.O.push([e,o]),o})}}return e},se=e=>{te(e,"connectedCallback")},re=(e,t={})=>{const n=[],l=t.exclude||[],o=c.customElements,s=a.head,r=s.querySelector("meta[charset]"),i=a.createElement("style"),u=[];let p,m=!0;Object.assign(f,t),f.l=new URL(t.resourcesUrl||"./",a.baseURI).href,t.syncQueue&&(f.t|=4),e.map(e=>e[1].map(t=>{const s={t:t[0],u:t[1],T:t[2],q:t[3]};s.T=t[2],s.q=t[3],s.O=[],s.W={};const r=s.u,i=class extends HTMLElement{constructor(e){super(e),ue(e=this,s),1&s.t&&e.attachShadow({mode:"open"})}connectedCallback(){p&&(clearTimeout(p),p=null),m?u.push(this):f.jmp(()=>(e=>{if(0==(1&f.t)){const t=ae(e),n=t.R,l=()=>{};if(1&t.t)$(e,t,n.q),se(t.s);else{t.t|=1,12&n.t&&(e=>{const t=e["s-cr"]=a.createComment("");t["s-cn"]=!0,e.insertBefore(t,e.firstChild)})(e);{let n=e;for(;n=n.parentNode||n.host;)if(n["s-p"]){J(t,t.C=n);break}}n.T&&Object.entries(n.T).map(([t,[n]])=>{if(31&n&&e.hasOwnProperty(t)){const n=e[t];delete e[t],e[t]=n}}),Se(()=>(async(e,t,n,l,o)=>{if(0==(32&t.t)){t.t|=32;{if((o=me(n)).then){const e=()=>{};o=await o,e()}o.isProxied||(n.W=o.watchers,oe(o,n,2),o.isProxied=!0);const e=()=>{};t.t|=8;try{new o(t)}catch(e){pe(e)}t.t&=-9,t.t|=128,e(),se(t.s)}const e=w(n);if(!he.has(e)&&o.style){const t=()=>{};((e,t,n)=>{let l=he.get(e);d&&n?(l=l||new CSSStyleSheet,l.replace(t)):l=t,he.set(e,l)})(e,o.style,!!(1&n.t)),t()}}const s=t.C,r=()=>K(t,!0);s&&s["s-rc"]?s["s-rc"].push(r):r()})(0,t,n))}l()}})(this))}disconnectedCallback(){f.jmp(()=>(()=>{if(0==(1&f.t)){const e=ae(this),t=e.s;e.o&&(e.o.map(e=>e()),e.o=void 0),te(t,"disconnectedCallback"),te(t,"componentDidUnload")}})())}forceUpdate(){(()=>{{const e=ae(this);e.M.isConnected&&2==(18&e.t)&&K(e,!1)}})()}componentOnReady(){return ae(this).A}};s.F=e[0],l.includes(r)||o.get(r)||(n.push(r),o.define(r,oe(i,s,1)))})),i.innerHTML=n+"{visibility:hidden}.hydrated{visibility:inherit}",i.setAttribute("data-styles",""),s.insertBefore(i,r?r.nextSibling:s.firstChild),m=!1,u.length?u.map(e=>e.connectedCallback()):f.jmp(()=>p=setTimeout(ee,30))},ie=(e,t)=>t in p?p[t]:"window"===t?c:"document"===t?a:"isServer"!==t&&"isPrerender"!==t&&("isClient"===t||("resourcesUrl"===t||"publicPath"===t?(()=>{const e=new URL(".",f.l);return e.origin!==c.location.origin?e.href:e.pathname})():"queue"===t?{write:Me,read:je,tick:{then:e=>Se(e)}}:void 0)),ce=new WeakMap,ae=e=>ce.get(e),fe=(e,t)=>ce.set(t.s=e,t),ue=(e,t)=>{const n={t:0,M:e,R:t,D:new Map};return n.A=new Promise(e=>n.P=e),e["s-p"]=[],e["s-rc"]=[],$(e,n,t.q),ce.set(e,n)},de=(e,t)=>t in e,pe=e=>console.error(e),$e=new Map,me=e=>{const t=e.u.replace(/-/g,"_"),n=e.F,l=$e.get(n);return l?l[t]:import(`./${n}.entry.js`).then(e=>($e.set(n,e),e[t]),pe)},he=new Map,ye=[],be=[],we=[],ke=(e,t)=>n=>{e.push(n),i||(i=!0,t&&4&f.t?Se(ge):f.raf(ge))},ve=(e,t)=>{let n=0,l=0;for(;n<e.length&&(l=performance.now())<t;)try{e[n++](l)}catch(e){pe(e)}n===e.length?e.length=0:0!==n&&e.splice(0,n)},ge=()=>{r++,(e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(e){pe(e)}e.length=0})(ye);{const e=2==(6&f.t)?performance.now()+14*Math.ceil(.1*r):1/0;ve(be,e),ve(we,e),be.length>0&&(we.push(...be),be.length=0),(i=ye.length+be.length+we.length>0)?f.raf(ge):r=0}},Se=e=>u().then(e),je=ke(ye,!1),Me=ke(be,!0),Ue=()=>u(),Ce=()=>{const e=import.meta.url,t={};return""!==e&&(t.resourcesUrl=new URL(".",e).href),u(t)};export{Ue as a,re as b,ie as c,G as d,B as g,g as h,Ce as p,fe as r}