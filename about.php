<?php
require_once 'includes/functions.php';

// Set page meta data
$pageTitle = 'About Us - Arcke Interior Design';
$pageDescription = 'Learn about Arcke Interior Design - Professional interior design services with expert engineers, innovative solutions, and award-winning projects for homes, offices, and commercial spaces.';

// Initialize default content if needed
initializeAboutPageContent();
initializeCompanyStats();

// Get dynamic content from database
$pageContent = getPageContent('about');
$services = getServices('active');
$companyStats = getCompanyStats();
$teamMembers = getTeamMembers(3);

// Get featured services for the about page (limit to 3)
$featuredServices = array_slice($services, 0, 3);

// If no services in database, use default ones
if (empty($featuredServices)) {
    $featuredServices = [
        [
            'id' => 1,
            'title' => 'Architecture Design',
            'description' => 'Conveniently streamline synergistic markets multidisciplinary products. Authoritatively wire bandwidth adaptive',
            'icon' => 'fas fa-drafting-compass'
        ],
        [
            'id' => 2,
            'title' => 'Interior Design',
            'description' => 'Conveniently streamline synergistic markets multidisciplinary products. Authoritatively wire bandwidth adaptive',
            'icon' => 'fas fa-couch'
        ],
        [
            'id' => 3,
            'title' => 'Furniture Design',
            'description' => 'Conveniently streamline synergistic markets multidisciplinary products. Authoritatively wire bandwidth adaptive',
            'icon' => 'fas fa-chair'
        ]
    ];
}

include 'includes/header-light.php';
?>

<!-- Hero Banner -->
<section class="position-relative" style="height: 70vh; min-height: 500px; background-image: url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'); background-size: cover; background-position: center; background-repeat: no-repeat;">
    <div class="position-absolute w-100 h-100 bg-black" style="opacity: 0.4;"></div>
    <div class="position-relative z-1 d-flex align-items-center justify-content-center h-100">
        <div class="text-center text-white">
            <h1 class="display-2 fw-bold mb-4" style="letter-spacing: 3px;">ABOUT US</h1>
        </div>
    </div>
    <div class="position-absolute bottom-0 end-0 m-4 bg-white text-dark rounded px-3 py-2 d-flex align-items-center shadow-sm" style="font-size: 0.875rem;">
        <i class="fas fa-home text-warning me-2"></i>
        <span class="fw-medium">HOME / ABOUT</span>
    </div>
</section>

<!-- Featured Services -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-dark mb-3">
                Featured <span class="text-warning">Services</span>
            </h2>
        </div>

        <div class="row g-4">
            <?php foreach ($featuredServices as $index => $service): ?>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm bg-white">
                    <div class="card-body p-4 text-center">
                        <div class="mb-4">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px;">
                                <i class="<?php echo $service['icon'] ?? 'fas fa-home'; ?> fs-2 text-warning"></i>
                            </div>
                        </div>
                        <h3 class="h5 fw-bold text-dark mb-3">
                            <?php echo htmlspecialchars($service['title']); ?>
                        </h3>
                        <p class="text-muted small" style="line-height: 1.6;">
                            <?php echo htmlspecialchars($service['description']); ?>
                        </p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Discover Arcke's Story -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-dark mb-3">
                Discover Arcke's <span class="text-warning">Story</span>
            </h2>
        </div>

        <div class="row g-5 align-items-center">
            <!-- Image Section -->
            <div class="col-lg-6">
                <div class="position-relative">
                    <img src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                         alt="Modern wooden house with stairs and bicycle outside under blue sky with clouds"
                         class="img-fluid rounded shadow-lg">
                    <div class="position-absolute top-0 start-0 w-100 h-100 bg-black bg-opacity-30 rounded d-flex align-items-center justify-content-center">
                        <button class="btn btn-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 70px; height: 70px;">
                            <i class="fas fa-play fs-5"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="col-lg-6">
                <ul class="nav nav-tabs border-0 mb-4" id="storyTabs" role="tablist">
                    <li class="nav-item me-4" role="presentation">
                        <button class="nav-link active border-0 bg-transparent text-dark fw-semibold px-0 pb-2" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="true" style="border-bottom: 2px solid #ffc107 !important;">HISTORY</button>
                    </li>
                    <li class="nav-item me-4" role="presentation">
                        <button class="nav-link border-0 bg-transparent text-muted fw-semibold px-0 pb-2" id="mission-tab" data-bs-toggle="tab" data-bs-target="#mission" type="button" role="tab" aria-controls="mission" aria-selected="false">MISSION</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link border-0 bg-transparent text-muted fw-semibold px-0 pb-2" id="vision-tab" data-bs-toggle="tab" data-bs-target="#vision" type="button" role="tab" aria-controls="vision" aria-selected="false">VISION</button>
                    </li>
                </ul>
                <div class="tab-content" id="storyTabsContent">
                    <div class="tab-pane fade show active" id="history" role="tabpanel" aria-labelledby="history-tab">
                        <p class="text-muted mb-4" style="line-height: 1.7;">
                            <?php
                            if ($pageContent && $pageContent['content']) {
                                echo htmlspecialchars(substr($pageContent['content'], 0, 300));
                            } else {
                                echo 'A wonderful serenity has taken possession of my entire soul, like these sweet mornings of spring which I enjoy with my whole heart. I am alone, and feel the charm of existence in this spot, which was created for the bliss of souls like mine. I am so happy, my dear friend, so absorbed in the exquisite sense of mere tranquil existence.';
                            }
                            ?>
                        </p>
                    </div>
                    <div class="tab-pane fade" id="mission" role="tabpanel" aria-labelledby="mission-tab">
                         <p class="text-muted mb-4" style="line-height: 1.7;">
                            Our mission is to create inspiring and functional spaces that enhance the lives of our clients. We strive for excellence in every project, delivering innovative design solutions with a focus on sustainability and client satisfaction.
                        </p>
                    </div>
                     <div class="tab-pane fade" id="vision" role="tabpanel" aria-labelledby="vision-tab">
                         <p class="text-muted mb-4" style="line-height: 1.7;">
                            Our vision is to be a leading force in the interior design industry, recognized for our creativity, quality, and commitment to transforming spaces into exceptional environments. We aim to set new standards in design and client collaboration.
                        </p>
                    </div>
                </div>

                <!-- Design Make Dream Card -->
                <div class="d-flex align-items-center bg-light p-3 rounded shadow-sm mt-4">
                    <div class="rounded me-3" style="width: 60px; height: 60px; background-image: url('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'); background-size: cover; background-position: center;"></div>
                    <div>
                        <h6 class="fw-bold text-dark mb-1">Design Make Dream</h6>
                        <p class="small text-muted mb-0">Dramatically administrate effective innovation.</p>
                    </div>
                </div>

                <!-- Progress Bars -->
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small text-muted fw-semibold">ARCHITECTURE</span>
                        <span class="small text-muted">80%</span>
                    </div>
                    <div class="progress mb-3" style="height: 6px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 80%;" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small text-muted fw-semibold">INTERIOR DESIGN</span>
                        <span class="small text-muted">70%</span>
                    </div>
                    <div class="progress mb-4" style="height: 6px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 70%;" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="bg-light p-4 rounded shadow-sm mt-4">
                    <h5 class="fw-bold text-dark mb-3">Drop Your Email</h5>
                    <form>
                        <div class="row g-2 mb-3">
                            <div class="col-6">
                                <input type="text" class="form-control" placeholder="Your Name" style="font-size: 0.875rem;">
                            </div>
                            <div class="col-6">
                                <input type="email" class="form-control" placeholder="Your Email" style="font-size: 0.875rem;">
                            </div>
                        </div>
                        <textarea class="form-control mb-3" rows="3" placeholder="Your Message" style="font-size: 0.875rem;"></textarea>
                        <button type="submit" class="btn btn-warning w-100 fw-semibold">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics -->
<section class="py-5 bg-light">
    <div class="container position-relative">
        <div class="dotted-bg-2 top-50 start-0 translate-middle-y" style="margin-left: -75px;"></div>
        <div class="dotted-bg-2 top-50 end-0 translate-middle-y" style="margin-right: -75px;"></div>
        <div class="row g-4 justify-content-center">
            <div class="col-6 col-md-3 text-center custom-hover-stat">
                <div class="display-4 fw-bold text-dark mb-2">
                    <?php echo htmlspecialchars($companyStats['projects']); ?>+
                </div>
                <div class="text-muted text-uppercase small fw-medium">
                    Projects
                </div>
            </div>
            <div class="col-6 col-md-3 text-center custom-hover-stat">
                <div class="display-4 fw-bold text-dark mb-2">
                    <?php echo htmlspecialchars($companyStats['engineers']); ?>
                </div>
                <div class="text-muted text-uppercase small fw-medium">
                    Engineers
                </div>
            </div>
            <div class="col-6 col-md-3 text-center custom-hover-stat">
                <div class="display-4 fw-bold text-dark mb-2">
                    <?php echo htmlspecialchars($companyStats['customers']); ?>K
                </div>
                <div class="text-muted text-uppercase small fw-medium">
                    Customers
                </div>
            </div>
            <div class="col-6 col-md-3 text-center custom-hover-stat">
                <div class="display-4 fw-bold text-dark mb-2">
                    <?php echo htmlspecialchars($companyStats['awards']); ?>+
                </div>
                <div class="text-muted text-uppercase small fw-medium">
                    Awards
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Arcke's Expert Engineers -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold text-dark mb-3">
                Arcke's Expert <span class="text-warning">Engineers</span>
            </h2>
            <div class="mx-auto mb-5" style="width: 80px; height: 4px; background-color: #ffc107;"></div>
            <p class="text-muted mt-4 mx-auto" style="max-width: 700px; font-weight: 300;">
                Meet our talented team of professionals dedicated to creating exceptional spaces.
            </p>
        </div>

        <div class="row g-4">
            <?php if (!empty($teamMembers)): ?>
                <?php foreach ($teamMembers as $member): ?>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm overflow-hidden custom-team-card">
                        <img src="<?php echo htmlspecialchars($member['image']); ?>"
                             alt="Portrait of <?php echo htmlspecialchars($member['name']); ?>"
                             class="card-img-top" style="height: 400px; object-fit: cover;">
                        <div class="card-img-overlay d-flex align-items-end bg-gradient-dark text-white p-4 custom-img-overlay">
                            <div class="text-center w-100">
                                <h4 class="h3 fw-bold mb-2"><?php echo htmlspecialchars($member['name']); ?></h4>
                                <p class="small mb-4" style="font-weight: 300;"><?php echo htmlspecialchars($member['position']); ?></p>
                                <div class="d-flex justify-content-center gap-3">
                                    <a href="#" class="text-white text-decoration-none custom-social-icon">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                    <a href="#" class="text-white text-decoration-none custom-social-icon">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    <a href="#" class="text-white text-decoration-none custom-social-icon">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
            <!-- Default team members -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm overflow-hidden custom-team-card">
                    <img src="https://storage.googleapis.com/a1aa/image/0dbe3656-42d0-4e73-c7fe-6f1179e99c1f.jpg"
                         alt="Portrait of a man with black suit, white shirt, black tie, and black glasses"
                         class="card-img-top" style="height: 400px; object-fit: cover;">
                    <div class="card-img-overlay d-flex align-items-end bg-gradient-dark text-white p-4 custom-img-overlay">
                        <div class="text-center w-100">
                            <h4 class="h3 fw-bold mb-2">JOHN SMITH</h4>
                            <p class="small mb-4" style="font-weight: 300;">SENIOR ENGINEER</p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm overflow-hidden custom-team-card">
                    <img src="https://storage.googleapis.com/a1aa/image/ec213fb7-60bd-4f1b-8dc3-f89160cf3805.jpg"
                         alt="Smiling woman with short hair and light blue shirt portrait"
                         class="card-img-top" style="height: 400px; object-fit: cover;">
                    <div class="card-img-overlay d-flex align-items-end bg-gradient-dark text-white p-4 custom-img-overlay">
                        <div class="text-center w-100">
                            <h4 class="h3 fw-bold mb-2">SARAH JOHNSON</h4>
                            <p class="small mb-4" style="font-weight: 300;">ARCHITECT</p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm overflow-hidden custom-team-card">
                    <img src="https://storage.googleapis.com/a1aa/image/937aecd8-9dda-417e-ad05-c239e67dd589.jpg"
                         alt="Man with beard and glasses wearing gray suit and white shirt portrait"
                         class="card-img-top" style="height: 400px; object-fit: cover;">
                    <div class="card-img-overlay d-flex align-items-end bg-gradient-dark text-white p-4 custom-img-overlay">
                        <div class="text-center w-100">
                            <h4 class="h3 fw-bold mb-2">MORRIS BARBER</h4>
                            <p class="small mb-4" style="font-weight: 300;">ENGINEER</p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="text-white text-decoration-none custom-social-icon">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Qualifications -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-4">
            <div class="d-flex align-items-center justify-content-center mb-4">
                <div class="flex-grow-1" style="height: 1px; background-color: #dee2e6;"></div>
                <p class="px-4 text-muted text-uppercase small fw-medium">
                    Our Professional Qualifications & Certifications
                </p>
                <div class="flex-grow-1" style="height: 1px; background-color: #dee2e6;"></div>
            </div>
            <div class="d-flex flex-wrap justify-content-center align-items-center gap-5 opacity-75">
                <img src="assets/images/qualifications/2.png"
                     alt="Professional Qualification Certificate" 
                     class="img-fluid custom-qualification-logo" style="height: 40px; filter: grayscale(100%);">
                <img src="assets/images/qualifications/3.png"
                     alt="Professional Qualification Certificate" 
                     class="img-fluid custom-qualification-logo" style="height: 40px; filter: grayscale(100%);">
                <img src="assets/images/qualifications/4.png"
                     alt="Professional Qualification Certificate" 
                     class="img-fluid custom-qualification-logo" style="height: 40px; filter: grayscale(100%);">
                <img src="assets/images/qualifications/5.png"
                     alt="Professional Qualification Certificate" 
                     class="img-fluid custom-qualification-logo" style="height: 40px; filter: grayscale(100%);">
                <img src="assets/images/qualifications/6.png"
                     alt="Professional Qualification Certificate" 
                     class="img-fluid custom-qualification-logo" style="height: 40px; filter: grayscale(100%);">
                <img src="assets/images/qualifications/7.png"
                     alt="Professional Qualification Certificate" 
                     class="img-fluid custom-qualification-logo" style="height: 40px; filter: grayscale(100%);">
                <img src="assets/images/qualifications/8.png"
                     alt="Professional Qualification Certificate" 
                     class="img-fluid custom-qualification-logo" style="height: 40px; filter: grayscale(100%);">
            </div>
        </div>
    </div>
</section>

<?php
// Set variables for footer
$socialLinks = [
    'facebook' => [
        'url' => '#',
        'icon' => 'fab fa-facebook-f',
        'label' => 'Follow us on Facebook'
    ],
    'twitter' => [
        'url' => '#',
        'icon' => 'fab fa-twitter',
        'label' => 'Follow us on Twitter'
    ],
    'linkedin' => [
        'url' => '#',
        'icon' => 'fab fa-linkedin-in',
        'label' => 'Connect with us on LinkedIn'
    ],
    'instagram' => [
        'url' => '#',
        'icon' => 'fab fa-instagram',
        'label' => 'Follow us on Instagram'
    ]
];
$isHomePage = false;

include 'includes/footer-light.php';
?>

<style>
/* Custom styles for Bootstrap overrides and additional features to match image reference */
.custom-breadcrumb {
    bottom: 2rem; /* Adjust based on visual */
    left: 2rem; /* Adjust based on visual */
    padding: 0.5rem 1.5rem; /* Adjust based on visual */
    font-size: 0.875rem; /* small font size */
}

.custom-hover-effect {
    transition: all 0.3s ease;
}
.custom-hover-effect:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
}

.custom-arrow-animation {
    transition: transform 0.3s ease;
}
.custom-hover-effect:hover .custom-arrow-animation {
    transform: translateX(5px);
}

.custom-icon-box {
    width: 60px;
    height: 60px;
}

.custom-hover-img {
    position: relative;
    overflow: hidden;
}

.custom-img-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-hover-img:hover .custom-img-overlay {
    opacity: 1;
}

.custom-hover-stat {
     transition: transform 0.3s ease;
}

.custom-hover-stat:hover {
    transform: scale(1.05);
}

.custom-team-card .custom-img-overlay {
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
}

.custom-social-icon {
    transition: transform 0.3s ease, color 0.3s ease;
}

.custom-social-icon:hover {
    transform: scale(1.1);
    color: #ffc107 !important; /* Bootstrap warning color */
}

.custom-qualification-logo {
    filter: grayscale(100%);
    transition: filter 0.3s ease, transform 0.3s ease;
}

.custom-qualification-logo:hover {
    filter: grayscale(0%);
    transform: scale(1.1);
}

.custom-tabs .nav-link {
    color: #6c757d; /* Muted text color */
    border: none;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px; /* Adjust to prevent gap */
    transition: color 0.3s ease, border-color 0.3s ease;
    text-transform: uppercase;
    font-size: 0.875rem; /* small font size */
    font-weight: 500; /* medium font weight */
    padding-left: 0;
    padding-right: 2rem;
}

.custom-tabs .nav-link:hover {
    color: #ffc107; /* Warning color */
    border-color: #feca57; /* Lighter warning for hover */
}

.custom-tabs .nav-link.active {
    color: #ffc107; /* Warning color */
    border-color: #ffc107; /* Warning color */
    background-color: transparent;
}

.custom-tabs {
    border-bottom: 1px solid #dee2e6; /* Light gray border */
}

.custom-contact-form .form-control-lg {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; /* Softer shadow */
}

.custom-btn-dark-hover {
    transition: background-color 0.3s ease, color 0.3s ease;
}

.custom-btn-dark-hover:hover {
    background-color: #ffc107 !important; /* Warning color */
    color: #212529 !important; /* Dark text color */
}

.dotted-bg-1 {
    background-image: radial-gradient(circle, #ccc 1px, transparent 1px);
    background-size: 10px 10px;
    position: absolute;
    width: 100px;
    height: 100px;
    opacity: 0.3;
    z-index: -1;
}

.dotted-bg-2 {
    background-image: radial-gradient(circle, #ccc 1px, transparent 1px);
    background-size: 10px 10px;
    position: absolute;
    width: 150px;
    height: 150px;
    opacity: 0.3;
    z-index: -1;
}

.service-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffc107;
    border: 1px solid #ffc107;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.section-watermark {
    position: absolute;
    font-size: 10rem;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.03);
    z-index: -1;
    white-space: nowrap;
    pointer-events: none;
}

.section-watermark.services {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-5deg);
}

.section-watermark.about-us {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(5deg);
}

.section-watermark.engineers {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-5deg);
}

</style>

<script>
// Activate Bootstrap tabs
var triggerTabList = [].slice.call(document.querySelectorAll('#storyTabs button'))
triggerTabList.forEach(function (triggerEl) {
  var tabTrigger = new bootstrap.Tab(triggerEl)

  triggerEl.addEventListener('click', function (event) {
    event.preventDefault()
    tabTrigger.show()
  })
})
</script>
