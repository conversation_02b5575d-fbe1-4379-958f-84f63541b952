import{r as t,h as i}from"./p-fbbae598.js";class o{constructor(i){t(this,i),this.a="",this.b=""}async componentWillLoad(){const t="/data/builds/master.json?ts="+Date.now(),i=await fetch(t);i.ok&&(this.build=await i.json())}onSubmit(t){t.preventDefault(),t.stopPropagation();let i=this.a.trim().toLowerCase(),o=this.b.trim().toLowerCase();i&&o&&(i=i.substring(0,7),o=o.substring(0,7),window.location.href=`/${i}/${o}`)}render(){return[i("header",null,i("div",{class:"logo"},i("a",{href:"/"},i("img",{src:"/assets/logo.png?1"})))),i("section",null,this.build?i("section",{class:"master"},i("p",null,i("a",{href:"/master"},this.build.message))):null,i("form",{onSubmit:this.onSubmit.bind(this)},i("div",null,i("input",{onInput:t=>this.a=t.target.value})),i("div",null,i("input",{onInput:t=>this.b=t.target.value})),i("div",null,i("button",{type:"submit"},"Compare Screenshots"))))]}}o.style="header{padding:8px;background:white;box-shadow:var(--header-box-shadow)}img{width:174px;height:32px}.logo{flex:1;padding:7px}a{padding:8px;color:var(--analysis-data-color);text-decoration:none}.master{text-align:center}a:hover{text-decoration:underline}form{width:160px;margin:40px auto}form div{margin:10px}input{width:100%}";export{o as screenshot_lookup}