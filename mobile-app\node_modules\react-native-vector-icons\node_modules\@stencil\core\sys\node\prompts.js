!function(t,e){for(var s in e)t[s]=e[s]}(exports,function(t){var e={};function s(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,s),r.l=!0,r.exports}return s.m=t,s.c=e,s.d=function(t,e,i){s.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},s.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(t,e){if(1&e&&(t=s(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(s.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)s.d(i,r,function(e){return t[e]}.bind(null,r));return i},s.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return s.d(e,"a",e),e},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.p="",s(s.s=8)}([function(t,e,s){"use strict";const{FORCE_COLOR:i,NODE_DISABLE_COLORS:r,TERM:n}=process.env,o={enabled:!r&&"dumb"!==n&&"0"!==i,reset:l(0,0),bold:l(1,22),dim:l(2,22),italic:l(3,23),underline:l(4,24),inverse:l(7,27),hidden:l(8,28),strikethrough:l(9,29),black:l(30,39),red:l(31,39),green:l(32,39),yellow:l(33,39),blue:l(34,39),magenta:l(35,39),cyan:l(36,39),white:l(37,39),gray:l(90,39),grey:l(90,39),bgBlack:l(40,49),bgRed:l(41,49),bgGreen:l(42,49),bgYellow:l(43,49),bgBlue:l(44,49),bgMagenta:l(45,49),bgCyan:l(46,49),bgWhite:l(47,49)};function h(t,e){let s,i=0,r="",n="";for(;i<t.length;i++)s=t[i],r+=s.open,n+=s.close,e.includes(s.close)&&(e=e.replace(s.rgx,s.close+s.open));return r+e+n}function l(t,e){let s={open:`[${t}m`,close:`[${e}m`,rgx:new RegExp(`\\x1b\\[${e}m`,"g")};return function(e){return void 0!==this&&void 0!==this.has?(this.has.includes(t)||(this.has.push(t),this.keys.push(s)),void 0===e?this:o.enabled?h(this.keys,e+""):e+""):void 0===e?function(t,e){let s={has:t,keys:e};return s.reset=o.reset.bind(s),s.bold=o.bold.bind(s),s.dim=o.dim.bind(s),s.italic=o.italic.bind(s),s.underline=o.underline.bind(s),s.inverse=o.inverse.bind(s),s.hidden=o.hidden.bind(s),s.strikethrough=o.strikethrough.bind(s),s.black=o.black.bind(s),s.red=o.red.bind(s),s.green=o.green.bind(s),s.yellow=o.yellow.bind(s),s.blue=o.blue.bind(s),s.magenta=o.magenta.bind(s),s.cyan=o.cyan.bind(s),s.white=o.white.bind(s),s.gray=o.gray.bind(s),s.grey=o.grey.bind(s),s.bgBlack=o.bgBlack.bind(s),s.bgRed=o.bgRed.bind(s),s.bgGreen=o.bgGreen.bind(s),s.bgYellow=o.bgYellow.bind(s),s.bgBlue=o.bgBlue.bind(s),s.bgMagenta=o.bgMagenta.bind(s),s.bgCyan=o.bgCyan.bind(s),s.bgWhite=o.bgWhite.bind(s),s}([t],[s]):o.enabled?h([s],e+""):e+""}}t.exports=o},function(t,e,s){"use strict";const i={to:(t,e)=>e?`[${e+1};${t+1}H`:`[${t+1}G`,move(t,e){let s="";return t<0?s+=`[${-t}D`:t>0&&(s+=`[${t}C`),e<0?s+=`[${-e}A`:e>0&&(s+=`[${e}B`),s},up:(t=1)=>`[${t}A`,down:(t=1)=>`[${t}B`,forward:(t=1)=>`[${t}C`,backward:(t=1)=>`[${t}D`,nextLine:(t=1)=>"[E".repeat(t),prevLine:(t=1)=>"[F".repeat(t),left:"[G",hide:"[?25l",show:"[?25h",save:"7",restore:"8"},r={up:(t=1)=>"[S".repeat(t),down:(t=1)=>"[T".repeat(t)},n={screen:"[2J",up:(t=1)=>"[1J".repeat(t),down:(t=1)=>"[J".repeat(t),line:"[2K",lineEnd:"[K",lineStart:"[1K",lines(t){let e="";for(let s=0;s<t;s++)e+=this.line+(s<t-1?i.up():"");return t&&(e+=i.left),e}};t.exports={cursor:i,scroll:r,erase:n,beep:""}},function(t,e,s){"use strict";t.exports={action:s(14),clear:s(15),style:s(16),strip:s(5),figures:s(6),lines:s(17),wrap:s(18),entriesToDisplay:s(19)}},function(t,e,s){"use strict";class i{constructor({token:t,date:e,parts:s,locales:i}){this.token=t,this.date=e||new Date,this.parts=s||[this],this.locales=i||{}}up(){}down(){}next(){const t=this.parts.indexOf(this);return this.parts.find(((e,s)=>s>t&&e instanceof i))}setTo(t){}prev(){let t=[].concat(this.parts).reverse();const e=t.indexOf(this);return t.find(((t,s)=>s>e&&t instanceof i))}toString(){return String(this.date)}}t.exports=i},function(t,e,s){"use strict";const i=s(13),{action:r}=s(2),n=s(20),{beep:o,cursor:h}=s(1),l=s(0);t.exports=class extends n{constructor(t={}){super(),this.firstRender=!0,this.in=t.stdin||process.stdin,this.out=t.stdout||process.stdout,this.onRender=(t.onRender||(()=>{})).bind(this);const e=i.createInterface({input:this.in,escapeCodeTimeout:50});i.emitKeypressEvents(this.in,e),this.in.isTTY&&this.in.setRawMode(!0);const s=["SelectPrompt","MultiselectPrompt"].indexOf(this.constructor.name)>-1,n=(t,e)=>{let i=r(e,s);!1===i?this._&&this._(t,e):"function"==typeof this[i]?this[i](e):this.bell()};this.close=()=>{this.out.write(h.show),this.in.removeListener("keypress",n),this.in.isTTY&&this.in.setRawMode(!1),e.close(),this.emit(this.aborted?"abort":this.exited?"exit":"submit",this.value),this.closed=!0},this.in.on("keypress",n)}fire(){this.emit("state",{value:this.value,aborted:!!this.aborted,exited:!!this.exited})}bell(){this.out.write(o)}render(){this.onRender(l),this.firstRender&&(this.firstRender=!1)}}},function(t,e,s){"use strict";t.exports=t=>{const e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[a-zA-Z\\d]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PRZcf-ntqry=><~]))"].join("|"),s=new RegExp(e,"g");return"string"==typeof t?t.replace(s,""):t}},function(t,e,s){"use strict";const i={arrowUp:"↑",arrowDown:"↓",arrowLeft:"←",arrowRight:"→",radioOn:"◉",radioOff:"◯",tick:"✔",cross:"✖",ellipsis:"…",pointerSmall:"›",line:"─",pointer:"❯"},r={arrowUp:i.arrowUp,arrowDown:i.arrowDown,arrowLeft:i.arrowLeft,arrowRight:i.arrowRight,radioOn:"(*)",radioOff:"( )",tick:"√",cross:"×",ellipsis:"...",pointerSmall:"»",line:"─",pointer:">"},n="win32"===process.platform?r:i;t.exports=n},function(t,e,s){"use strict";const i=s(0),{cursor:r}=s(1),n=s(4),{clear:o,figures:h,style:l,wrap:a,entriesToDisplay:u}=s(2);t.exports=class extends n{constructor(t={}){super(t),this.msg=t.message,this.cursor=t.cursor||0,this.scrollIndex=t.cursor||0,this.hint=t.hint||"",this.warn=t.warn||"- This option is disabled -",this.minSelected=t.min,this.showMinError=!1,this.maxChoices=t.max,this.instructions=t.instructions,this.optionsPerPage=t.optionsPerPage||10,this.value=t.choices.map(((t,e)=>("string"==typeof t&&(t={title:t,value:e}),{title:t&&(t.title||t.value||t),description:t&&t.description,value:t&&(void 0===t.value?e:t.value),selected:t&&t.selected,disabled:t&&t.disabled}))),this.clear=o("",this.out.columns),t.overrideRender||this.render()}reset(){this.value.map((t=>!t.selected)),this.cursor=0,this.fire(),this.render()}selected(){return this.value.filter((t=>t.selected))}exit(){this.abort()}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write("\n"),this.close()}submit(){const t=this.value.filter((t=>t.selected));this.minSelected&&t.length<this.minSelected?(this.showMinError=!0,this.render()):(this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write("\n"),this.close())}first(){this.cursor=0,this.render()}last(){this.cursor=this.value.length-1,this.render()}next(){this.cursor=(this.cursor+1)%this.value.length,this.render()}up(){0===this.cursor?this.cursor=this.value.length-1:this.cursor--,this.render()}down(){this.cursor===this.value.length-1?this.cursor=0:this.cursor++,this.render()}left(){this.value[this.cursor].selected=!1,this.render()}right(){if(this.value.filter((t=>t.selected)).length>=this.maxChoices)return this.bell();this.value[this.cursor].selected=!0,this.render()}handleSpaceToggle(){const t=this.value[this.cursor];if(t.selected)t.selected=!1,this.render();else{if(t.disabled||this.value.filter((t=>t.selected)).length>=this.maxChoices)return this.bell();t.selected=!0,this.render()}}toggleAll(){if(void 0!==this.maxChoices||this.value[this.cursor].disabled)return this.bell();const t=!this.value[this.cursor].selected;this.value.filter((t=>!t.disabled)).forEach((e=>e.selected=t)),this.render()}_(t,e){if(" "===t)this.handleSpaceToggle();else{if("a"!==t)return this.bell();this.toggleAll()}}renderInstructions(){return void 0===this.instructions||this.instructions?"string"==typeof this.instructions?this.instructions:`\nInstructions:\n    ${h.arrowUp}/${h.arrowDown}: Highlight option\n    ${h.arrowLeft}/${h.arrowRight}/[space]: Toggle selection\n`+(void 0===this.maxChoices?"    a: Toggle all\n":"")+"    enter/return: Complete answer":""}renderOption(t,e,s,r){const n=(e.selected?i.green(h.radioOn):h.radioOff)+" "+r+" ";let o,l;return e.disabled?o=t===s?i.gray().underline(e.title):i.strikethrough().gray(e.title):(o=t===s?i.cyan().underline(e.title):e.title,t===s&&e.description&&(l=` - ${e.description}`,(n.length+o.length+l.length>=this.out.columns||e.description.split(/\r?\n/).length>1)&&(l="\n"+a(e.description,{margin:n.length,width:this.out.columns})))),n+o+i.gray(l||"")}paginateOptions(t){if(0===t.length)return i.red("No matches for this query.");let e,{startIndex:s,endIndex:r}=u(this.cursor,t.length,this.optionsPerPage),n=[];for(let i=s;i<r;i++)e=i===s&&s>0?h.arrowUp:i===r-1&&r<t.length?h.arrowDown:" ",n.push(this.renderOption(this.cursor,t[i],i,e));return"\n"+n.join("\n")}renderOptions(t){return this.done?"":this.paginateOptions(t)}renderDoneOrInstructions(){if(this.done)return this.value.filter((t=>t.selected)).map((t=>t.title)).join(", ");const t=[i.gray(this.hint),this.renderInstructions()];return this.value[this.cursor].disabled&&t.push(i.yellow(this.warn)),t.join(" ")}render(){if(this.closed)return;this.firstRender&&this.out.write(r.hide),super.render();let t=[l.symbol(this.done,this.aborted),i.bold(this.msg),l.delimiter(!1),this.renderDoneOrInstructions()].join(" ");this.showMinError&&(t+=i.red(`You must select a minimum of ${this.minSelected} choices.`),this.showMinError=!1),t+=this.renderOptions(this.value),this.out.write(this.clear+t),this.clear=o(t,this.out.columns)}}},function(t,e,s){t.exports=s(9)},function(t,e,s){"use strict";const i=s(10),r=["suggest","format","onState","validate","onRender","type"],n=()=>{};async function o(t=[],{onSubmit:e=n,onCancel:s=n}={}){const l={},a=o._override||{};let u,d,c,p,g,f;t=[].concat(t);const m=async(t,e,s=!1)=>{if(s||!t.validate||!0===t.validate(e))return t.format?await t.format(e,l):e};for(d of t)if(({name:p,type:g}=d),"function"==typeof g&&(g=await g(u,{...l},d),d.type=g),g){for(let t in d){if(r.includes(t))continue;let e=d[t];d[t]="function"==typeof e?await e(u,{...l},f):e}if(f=d,"string"!=typeof d.message)throw new Error("prompt message is required");if(({name:p,type:g}=d),void 0===i[g])throw new Error(`prompt type (${g}) is not defined`);if(void 0===a[d.name]||(u=await m(d,a[d.name]),void 0===u)){try{u=o._injected?h(o._injected,d.initial):await i[g](d),l[p]=u=await m(d,u,!0),c=await e(d,u,l)}catch(t){c=!await s(d,l)}if(c)return l}else l[p]=u}return l}function h(t,e){const s=t.shift();if(s instanceof Error)throw s;return void 0===s?e:s}t.exports=Object.assign(o,{prompt:o,prompts:i,inject:function(t){o._injected=(o._injected||[]).concat(t)},override:function(t){o._override=Object.assign({},t)}})},function(t,e,s){"use strict";const i=e,r=s(11),n=t=>t;function o(t,e,s={}){return new Promise(((i,o)=>{const h=new r[t](e),l=s.onAbort||n,a=s.onSubmit||n,u=s.onExit||n;h.on("state",e.onState||n),h.on("submit",(t=>i(a(t)))),h.on("exit",(t=>i(u(t)))),h.on("abort",(t=>o(l(t))))}))}i.text=t=>o("TextPrompt",t),i.password=t=>(t.style="password",i.text(t)),i.invisible=t=>(t.style="invisible",i.text(t)),i.number=t=>o("NumberPrompt",t),i.date=t=>o("DatePrompt",t),i.confirm=t=>o("ConfirmPrompt",t),i.list=t=>{const e=t.separator||",";return o("TextPrompt",t,{onSubmit:t=>t.split(e).map((t=>t.trim()))})},i.toggle=t=>o("TogglePrompt",t),i.select=t=>o("SelectPrompt",t),i.multiselect=t=>{t.choices=[].concat(t.choices||[]);const e=t=>t.filter((t=>t.selected)).map((t=>t.value));return o("MultiselectPrompt",t,{onAbort:e,onSubmit:e})},i.autocompleteMultiselect=t=>{t.choices=[].concat(t.choices||[]);const e=t=>t.filter((t=>t.selected)).map((t=>t.value));return o("AutocompleteMultiselectPrompt",t,{onAbort:e,onSubmit:e})};const h=(t,e)=>Promise.resolve(e.filter((e=>e.title.slice(0,t.length).toLowerCase()===t.toLowerCase())));i.autocomplete=t=>(t.suggest=t.suggest||h,t.choices=[].concat(t.choices||[]),o("AutocompletePrompt",t))},function(t,e,s){"use strict";t.exports={TextPrompt:s(12),SelectPrompt:s(21),TogglePrompt:s(22),DatePrompt:s(23),NumberPrompt:s(33),MultiselectPrompt:s(7),AutocompletePrompt:s(34),AutocompleteMultiselectPrompt:s(35),ConfirmPrompt:s(36)}},function(t,e,s){const i=s(0),r=s(4),{erase:n,cursor:o}=s(1),{style:h,clear:l,lines:a,figures:u}=s(2);t.exports=class extends r{constructor(t={}){super(t),this.transform=h.render(t.style),this.scale=this.transform.scale,this.msg=t.message,this.initial=t.initial||"",this.validator=t.validate||(()=>!0),this.value="",this.errorMsg=t.error||"Please Enter A Valid Value",this.cursor=Number(!!this.initial),this.clear=l("",this.out.columns),this.render()}set value(t){!t&&this.initial?(this.placeholder=!0,this.rendered=i.gray(this.transform.render(this.initial))):(this.placeholder=!1,this.rendered=this.transform.render(t)),this._value=t,this.fire()}get value(){return this._value}reset(){this.value="",this.cursor=Number(!!this.initial),this.fire(),this.render()}exit(){this.abort()}abort(){this.value=this.value||this.initial,this.done=this.aborted=!0,this.error=!1,this.red=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}async validate(){let t=await this.validator(this.value);"string"==typeof t&&(this.errorMsg=t,t=!1),this.error=!t}async submit(){if(this.value=this.value||this.initial,await this.validate(),this.error)return this.red=!0,this.fire(),void this.render();this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}next(){if(!this.placeholder)return this.bell();this.value=this.initial,this.cursor=this.rendered.length,this.fire(),this.render()}moveCursor(t){this.placeholder||(this.cursor=this.cursor+t)}_(t,e){let s=this.value.slice(0,this.cursor),i=this.value.slice(this.cursor);this.value=`${s}${t}${i}`,this.red=!1,this.cursor=this.placeholder?0:s.length+1,this.render()}delete(){if(0===this.cursor)return this.bell();let t=this.value.slice(0,this.cursor-1),e=this.value.slice(this.cursor);this.value=`${t}${e}`,this.red=!1,this.moveCursor(-1),this.render()}deleteForward(){if(this.cursor*this.scale>=this.rendered.length||this.placeholder)return this.bell();let t=this.value.slice(0,this.cursor),e=this.value.slice(this.cursor+1);this.value=`${t}${e}`,this.red=!1,this.render()}first(){this.cursor=0,this.render()}last(){this.cursor=this.value.length,this.render()}left(){if(this.cursor<=0||this.placeholder)return this.bell();this.moveCursor(-1),this.render()}right(){if(this.cursor*this.scale>=this.rendered.length||this.placeholder)return this.bell();this.moveCursor(1),this.render()}render(){this.closed||(this.firstRender||(this.outputError&&this.out.write(o.down(a(this.outputError,this.out.columns)-1)+l(this.outputError,this.out.columns)),this.out.write(l(this.outputText,this.out.columns))),super.render(),this.outputError="",this.outputText=[h.symbol(this.done,this.aborted),i.bold(this.msg),h.delimiter(this.done),this.red?i.red(this.rendered):this.rendered].join(" "),this.error&&(this.outputError+=this.errorMsg.split("\n").reduce(((t,e,s)=>t+`\n${s?" ":u.pointerSmall} ${i.red().italic(e)}`),"")),this.out.write(n.line+o.to(0)+this.outputText+o.save+this.outputError+o.restore))}}},function(t,e){t.exports=require("readline")},function(t,e,s){"use strict";t.exports=(t,e)=>{if(!t.meta||"escape"===t.name){if(t.ctrl){if("a"===t.name)return"first";if("c"===t.name)return"abort";if("d"===t.name)return"abort";if("e"===t.name)return"last";if("g"===t.name)return"reset"}if(e){if("j"===t.name)return"down";if("k"===t.name)return"up"}return"return"===t.name||"enter"===t.name?"submit":"backspace"===t.name?"delete":"delete"===t.name?"deleteForward":"abort"===t.name?"abort":"escape"===t.name?"exit":"tab"===t.name?"next":"pagedown"===t.name?"nextPage":"pageup"===t.name?"prevPage":"home"===t.name?"home":"end"===t.name?"end":"up"===t.name?"up":"down"===t.name?"down":"right"===t.name?"right":"left"===t.name&&"left"}}},function(t,e,s){"use strict";const i=s(5),{erase:r,cursor:n}=s(1);t.exports=function(t,e){if(!e)return r.line+n.to(0);let s=0;const o=t.split(/\r?\n/);for(let t of o)s+=1+Math.floor(Math.max([...i(t)].length-1,0)/e);return r.lines(s)}},function(t,e,s){"use strict";const i=s(0),r=s(6),n=Object.freeze({password:{scale:1,render:t=>"*".repeat(t.length)},emoji:{scale:2,render:t=>"😃".repeat(t.length)},invisible:{scale:0,render:t=>""},default:{scale:1,render:t=>`${t}`}}),o=Object.freeze({aborted:i.red(r.cross),done:i.green(r.tick),exited:i.yellow(r.cross),default:i.cyan("?")});t.exports={styles:n,render:t=>n[t]||n.default,symbols:o,symbol:(t,e,s)=>e?o.aborted:s?o.exited:t?o.done:o.default,delimiter:t=>i.gray(t?r.ellipsis:r.pointerSmall),item:(t,e)=>i.gray(t?e?r.pointerSmall:"+":r.line)}},function(t,e,s){"use strict";const i=s(5);t.exports=function(t,e){let s=String(i(t)||"").split(/\r?\n/);return e?s.map((t=>Math.ceil(t.length/e))).reduce(((t,e)=>t+e)):s.length}},function(t,e,s){"use strict";t.exports=(t,e={})=>{const s=Number.isSafeInteger(parseInt(e.margin))?new Array(parseInt(e.margin)).fill(" ").join(""):e.margin||"",i=e.width;return(t||"").split(/\r?\n/g).map((t=>t.split(/\s+/g).reduce(((t,e)=>(e.length+s.length>=i||t[t.length-1].length+e.length+1<i?t[t.length-1]+=` ${e}`:t.push(`${s}${e}`),t)),[s]).join("\n"))).join("\n")}},function(t,e,s){"use strict";t.exports=(t,e,s)=>{s=s||e;let i=Math.min(e-s,t-Math.floor(s/2));return i<0&&(i=0),{startIndex:i,endIndex:Math.min(i+s,e)}}},function(t,e){t.exports=require("events")},function(t,e,s){"use strict";const i=s(0),r=s(4),{style:n,clear:o,figures:h,wrap:l,entriesToDisplay:a}=s(2),{cursor:u}=s(1);t.exports=class extends r{constructor(t={}){super(t),this.msg=t.message,this.hint=t.hint||"- Use arrow-keys. Return to submit.",this.warn=t.warn||"- This option is disabled",this.cursor=t.initial||0,this.choices=t.choices.map(((t,e)=>("string"==typeof t&&(t={title:t,value:e}),{title:t&&(t.title||t.value||t),value:t&&(void 0===t.value?e:t.value),description:t&&t.description,selected:t&&t.selected,disabled:t&&t.disabled}))),this.optionsPerPage=t.optionsPerPage||10,this.value=(this.choices[this.cursor]||{}).value,this.clear=o("",this.out.columns),this.render()}moveCursor(t){this.cursor=t,this.value=this.choices[t].value,this.fire()}reset(){this.moveCursor(0),this.fire(),this.render()}exit(){this.abort()}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write("\n"),this.close()}submit(){this.selection.disabled?this.bell():(this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write("\n"),this.close())}first(){this.moveCursor(0),this.render()}last(){this.moveCursor(this.choices.length-1),this.render()}up(){0===this.cursor?this.moveCursor(this.choices.length-1):this.moveCursor(this.cursor-1),this.render()}down(){this.cursor===this.choices.length-1?this.moveCursor(0):this.moveCursor(this.cursor+1),this.render()}next(){this.moveCursor((this.cursor+1)%this.choices.length),this.render()}_(t,e){if(" "===t)return this.submit()}get selection(){return this.choices[this.cursor]}render(){if(this.closed)return;this.firstRender?this.out.write(u.hide):this.out.write(o(this.outputText,this.out.columns)),super.render();let{startIndex:t,endIndex:e}=a(this.cursor,this.choices.length,this.optionsPerPage);if(this.outputText=[n.symbol(this.done,this.aborted),i.bold(this.msg),n.delimiter(!1),this.done?this.selection.title:this.selection.disabled?i.yellow(this.warn):i.gray(this.hint)].join(" "),!this.done){this.outputText+="\n";for(let s=t;s<e;s++){let r,n,o="",a=this.choices[s];n=s===t&&t>0?h.arrowUp:s===e-1&&e<this.choices.length?h.arrowDown:" ",a.disabled?(r=this.cursor===s?i.gray().underline(a.title):i.strikethrough().gray(a.title),n=(this.cursor===s?i.bold().gray(h.pointer)+" ":"  ")+n):(r=this.cursor===s?i.cyan().underline(a.title):a.title,n=(this.cursor===s?i.cyan(h.pointer)+" ":"  ")+n,a.description&&this.cursor===s&&(o=` - ${a.description}`,(n.length+r.length+o.length>=this.out.columns||a.description.split(/\r?\n/).length>1)&&(o="\n"+l(a.description,{margin:3,width:this.out.columns})))),this.outputText+=`${n} ${r}${i.gray(o)}\n`}}this.out.write(this.outputText)}}},function(t,e,s){const i=s(0),r=s(4),{style:n,clear:o}=s(2),{cursor:h,erase:l}=s(1);t.exports=class extends r{constructor(t={}){super(t),this.msg=t.message,this.value=!!t.initial,this.active=t.active||"on",this.inactive=t.inactive||"off",this.initialValue=this.value,this.render()}reset(){this.value=this.initialValue,this.fire(),this.render()}exit(){this.abort()}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write("\n"),this.close()}submit(){this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}deactivate(){if(!1===this.value)return this.bell();this.value=!1,this.render()}activate(){if(!0===this.value)return this.bell();this.value=!0,this.render()}delete(){this.deactivate()}left(){this.deactivate()}right(){this.activate()}down(){this.deactivate()}up(){this.activate()}next(){this.value=!this.value,this.fire(),this.render()}_(t,e){if(" "===t)this.value=!this.value;else if("1"===t)this.value=!0;else{if("0"!==t)return this.bell();this.value=!1}this.render()}render(){this.closed||(this.firstRender?this.out.write(h.hide):this.out.write(o(this.outputText,this.out.columns)),super.render(),this.outputText=[n.symbol(this.done,this.aborted),i.bold(this.msg),n.delimiter(this.done),this.value?this.inactive:i.cyan().underline(this.inactive),i.gray("/"),this.value?i.cyan().underline(this.active):this.active].join(" "),this.out.write(l.line+h.to(0)+this.outputText))}}},function(t,e,s){"use strict";const i=s(0),r=s(4),{style:n,clear:o,figures:h}=s(2),{erase:l,cursor:a}=s(1),{DatePart:u,Meridiem:d,Day:c,Hours:p,Milliseconds:g,Minutes:f,Month:m,Seconds:b,Year:v}=s(24),w=/\\(.)|"((?:\\["\\]|[^"])+)"|(D[Do]?|d{3,4}|d)|(M{1,4})|(YY(?:YY)?)|([aA])|([Hh]{1,2})|(m{1,2})|(s{1,2})|(S{1,4})|./g,y={1:({token:t})=>t.replace(/\\(.)/g,"$1"),2:t=>new c(t),3:t=>new m(t),4:t=>new v(t),5:t=>new d(t),6:t=>new p(t),7:t=>new f(t),8:t=>new b(t),9:t=>new g(t)},x={months:"January,February,March,April,May,June,July,August,September,October,November,December".split(","),monthsShort:"Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec".split(","),weekdays:"Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday".split(","),weekdaysShort:"Sun,Mon,Tue,Wed,Thu,Fri,Sat".split(",")};t.exports=class extends r{constructor(t={}){super(t),this.msg=t.message,this.cursor=0,this.typed="",this.locales=Object.assign(x,t.locales),this._date=t.initial||new Date,this.errorMsg=t.error||"Please Enter A Valid Value",this.validator=t.validate||(()=>!0),this.mask=t.mask||"YYYY-MM-DD HH:mm:ss",this.clear=o("",this.out.columns),this.render()}get value(){return this.date}get date(){return this._date}set date(t){t&&this._date.setTime(t.getTime())}set mask(t){let e;for(this.parts=[];e=w.exec(t);){let t=e.shift(),s=e.findIndex((t=>null!=t));this.parts.push(s in y?y[s]({token:e[s]||t,date:this.date,parts:this.parts,locales:this.locales}):e[s]||t)}let s=this.parts.reduce(((t,e)=>("string"==typeof e&&"string"==typeof t[t.length-1]?t[t.length-1]+=e:t.push(e),t)),[]);this.parts.splice(0),this.parts.push(...s),this.reset()}moveCursor(t){this.typed="",this.cursor=t,this.fire()}reset(){this.moveCursor(this.parts.findIndex((t=>t instanceof u))),this.fire(),this.render()}exit(){this.abort()}abort(){this.done=this.aborted=!0,this.error=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}async validate(){let t=await this.validator(this.value);"string"==typeof t&&(this.errorMsg=t,t=!1),this.error=!t}async submit(){if(await this.validate(),this.error)return this.color="red",this.fire(),void this.render();this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}up(){this.typed="",this.parts[this.cursor].up(),this.render()}down(){this.typed="",this.parts[this.cursor].down(),this.render()}left(){let t=this.parts[this.cursor].prev();if(null==t)return this.bell();this.moveCursor(this.parts.indexOf(t)),this.render()}right(){let t=this.parts[this.cursor].next();if(null==t)return this.bell();this.moveCursor(this.parts.indexOf(t)),this.render()}next(){let t=this.parts[this.cursor].next();this.moveCursor(t?this.parts.indexOf(t):this.parts.findIndex((t=>t instanceof u))),this.render()}_(t){/\d/.test(t)&&(this.typed+=t,this.parts[this.cursor].setTo(this.typed),this.render())}render(){this.closed||(this.firstRender?this.out.write(a.hide):this.out.write(o(this.outputText,this.out.columns)),super.render(),this.outputText=[n.symbol(this.done,this.aborted),i.bold(this.msg),n.delimiter(!1),this.parts.reduce(((t,e,s)=>t.concat(s!==this.cursor||this.done?e:i.cyan().underline(e.toString()))),[]).join("")].join(" "),this.error&&(this.outputText+=this.errorMsg.split("\n").reduce(((t,e,s)=>t+`\n${s?" ":h.pointerSmall} ${i.red().italic(e)}`),"")),this.out.write(l.line+a.to(0)+this.outputText))}}},function(t,e,s){"use strict";t.exports={DatePart:s(3),Meridiem:s(25),Day:s(26),Hours:s(27),Milliseconds:s(28),Minutes:s(29),Month:s(30),Seconds:s(31),Year:s(32)}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setHours((this.date.getHours()+12)%24)}down(){this.up()}toString(){let t=this.date.getHours()>12?"pm":"am";return/\A/.test(this.token)?t.toUpperCase():t}}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setDate(this.date.getDate()+1)}down(){this.date.setDate(this.date.getDate()-1)}setTo(t){this.date.setDate(parseInt(t.substr(-2)))}toString(){let t=this.date.getDate(),e=this.date.getDay();return"DD"===this.token?String(t).padStart(2,"0"):"Do"===this.token?t+(s=t,1==(s%=10)?"st":2===s?"nd":3===s?"rd":"th"):"d"===this.token?e+1:"ddd"===this.token?this.locales.weekdaysShort[e]:"dddd"===this.token?this.locales.weekdays[e]:t;var s}}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setHours(this.date.getHours()+1)}down(){this.date.setHours(this.date.getHours()-1)}setTo(t){this.date.setHours(parseInt(t.substr(-2)))}toString(){let t=this.date.getHours();return/h/.test(this.token)&&(t=t%12||12),this.token.length>1?String(t).padStart(2,"0"):t}}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setMilliseconds(this.date.getMilliseconds()+1)}down(){this.date.setMilliseconds(this.date.getMilliseconds()-1)}setTo(t){this.date.setMilliseconds(parseInt(t.substr(-this.token.length)))}toString(){return String(this.date.getMilliseconds()).padStart(4,"0").substr(0,this.token.length)}}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setMinutes(this.date.getMinutes()+1)}down(){this.date.setMinutes(this.date.getMinutes()-1)}setTo(t){this.date.setMinutes(parseInt(t.substr(-2)))}toString(){let t=this.date.getMinutes();return this.token.length>1?String(t).padStart(2,"0"):t}}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setMonth(this.date.getMonth()+1)}down(){this.date.setMonth(this.date.getMonth()-1)}setTo(t){t=parseInt(t.substr(-2))-1,this.date.setMonth(t<0?0:t)}toString(){let t=this.date.getMonth(),e=this.token.length;return 2===e?String(t+1).padStart(2,"0"):3===e?this.locales.monthsShort[t]:4===e?this.locales.months[t]:String(t+1)}}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setSeconds(this.date.getSeconds()+1)}down(){this.date.setSeconds(this.date.getSeconds()-1)}setTo(t){this.date.setSeconds(parseInt(t.substr(-2)))}toString(){let t=this.date.getSeconds();return this.token.length>1?String(t).padStart(2,"0"):t}}},function(t,e,s){"use strict";const i=s(3);t.exports=class extends i{constructor(t={}){super(t)}up(){this.date.setFullYear(this.date.getFullYear()+1)}down(){this.date.setFullYear(this.date.getFullYear()-1)}setTo(t){this.date.setFullYear(t.substr(-4))}toString(){let t=String(this.date.getFullYear()).padStart(4,"0");return 2===this.token.length?t.substr(-2):t}}},function(t,e,s){const i=s(0),r=s(4),{cursor:n,erase:o}=s(1),{style:h,figures:l,clear:a,lines:u}=s(2),d=/[0-9]/,c=t=>void 0!==t,p=(t,e)=>{let s=Math.pow(10,e);return Math.round(t*s)/s};t.exports=class extends r{constructor(t={}){super(t),this.transform=h.render(t.style),this.msg=t.message,this.initial=c(t.initial)?t.initial:"",this.float=!!t.float,this.round=t.round||2,this.inc=t.increment||1,this.min=c(t.min)?t.min:-1/0,this.max=c(t.max)?t.max:1/0,this.errorMsg=t.error||"Please Enter A Valid Value",this.validator=t.validate||(()=>!0),this.color="cyan",this.value="",this.typed="",this.lastHit=0,this.render()}set value(t){t||0===t?(this.placeholder=!1,this.rendered=this.transform.render(`${p(t,this.round)}`),this._value=p(t,this.round)):(this.placeholder=!0,this.rendered=i.gray(this.transform.render(`${this.initial}`)),this._value=""),this.fire()}get value(){return this._value}parse(t){return this.float?parseFloat(t):parseInt(t)}valid(t){return"-"===t||"."===t&&this.float||d.test(t)}reset(){this.typed="",this.value="",this.fire(),this.render()}exit(){this.abort()}abort(){let t=this.value;this.value=""!==t?t:this.initial,this.done=this.aborted=!0,this.error=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}async validate(){let t=await this.validator(this.value);"string"==typeof t&&(this.errorMsg=t,t=!1),this.error=!t}async submit(){if(await this.validate(),this.error)return this.color="red",this.fire(),void this.render();let t=this.value;this.value=""!==t?t:this.initial,this.done=!0,this.aborted=!1,this.error=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}up(){if(this.typed="",""===this.value&&(this.value=this.min-this.inc),this.value>=this.max)return this.bell();this.value+=this.inc,this.color="cyan",this.fire(),this.render()}down(){if(this.typed="",""===this.value&&(this.value=this.min+this.inc),this.value<=this.min)return this.bell();this.value-=this.inc,this.color="cyan",this.fire(),this.render()}delete(){let t=this.value.toString();if(0===t.length)return this.bell();this.value=this.parse(t=t.slice(0,-1))||"",""!==this.value&&this.value<this.min&&(this.value=this.min),this.color="cyan",this.fire(),this.render()}next(){this.value=this.initial,this.fire(),this.render()}_(t,e){if(!this.valid(t))return this.bell();const s=Date.now();if(s-this.lastHit>1e3&&(this.typed=""),this.typed+=t,this.lastHit=s,this.color="cyan","."===t)return this.fire();this.value=Math.min(this.parse(this.typed),this.max),this.value>this.max&&(this.value=this.max),this.value<this.min&&(this.value=this.min),this.fire(),this.render()}render(){this.closed||(this.firstRender||(this.outputError&&this.out.write(n.down(u(this.outputError,this.out.columns)-1)+a(this.outputError,this.out.columns)),this.out.write(a(this.outputText,this.out.columns))),super.render(),this.outputError="",this.outputText=[h.symbol(this.done,this.aborted),i.bold(this.msg),h.delimiter(this.done),this.done&&(this.done||this.placeholder)?this.rendered:i[this.color]().underline(this.rendered)].join(" "),this.error&&(this.outputError+=this.errorMsg.split("\n").reduce(((t,e,s)=>t+`\n${s?" ":l.pointerSmall} ${i.red().italic(e)}`),"")),this.out.write(o.line+n.to(0)+this.outputText+n.save+this.outputError+n.restore))}}},function(t,e,s){"use strict";const i=s(0),r=s(4),{erase:n,cursor:o}=s(1),{style:h,clear:l,figures:a,wrap:u,entriesToDisplay:d}=s(2),c=(t,e)=>t[e]&&(t[e].value||t[e].title||t[e]),p=(t,e)=>t[e]&&(t[e].title||t[e].value||t[e]);t.exports=class extends r{constructor(t={}){super(t),this.msg=t.message,this.suggest=t.suggest,this.choices=t.choices,this.initial="number"==typeof t.initial?t.initial:((t,e)=>{const s=t.findIndex((t=>t.value===e||t.title===e));return s>-1?s:void 0})(t.choices,t.initial),this.select=this.initial||t.cursor||0,this.i18n={noMatches:t.noMatches||"no matches found"},this.fallback=t.fallback||this.initial,this.clearFirst=t.clearFirst||!1,this.suggestions=[],this.input="",this.limit=t.limit||10,this.cursor=0,this.transform=h.render(t.style),this.scale=this.transform.scale,this.render=this.render.bind(this),this.complete=this.complete.bind(this),this.clear=l("",this.out.columns),this.complete(this.render),this.render()}set fallback(t){this._fb=Number.isSafeInteger(parseInt(t))?parseInt(t):t}get fallback(){let t;return"number"==typeof this._fb?t=this.choices[this._fb]:"string"==typeof this._fb&&(t={title:this._fb}),t||this._fb||{title:this.i18n.noMatches}}moveSelect(t){this.select=t,this.suggestions.length>0?this.value=c(this.suggestions,t):this.value=this.fallback.value,this.fire()}async complete(t){const e=this.completing=this.suggest(this.input,this.choices),s=await e;if(this.completing!==e)return;this.suggestions=s.map(((t,e,s)=>({title:p(s,e),value:c(s,e),description:t.description}))),this.completing=!1;const i=Math.max(s.length-1,0);this.moveSelect(Math.min(i,this.select)),t&&t()}reset(){this.input="",this.complete((()=>{this.moveSelect(void 0!==this.initial?this.initial:0),this.render()})),this.render()}exit(){this.clearFirst&&this.input.length>0?this.reset():(this.done=this.exited=!0,this.aborted=!1,this.fire(),this.render(),this.out.write("\n"),this.close())}abort(){this.done=this.aborted=!0,this.exited=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}submit(){this.done=!0,this.aborted=this.exited=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}_(t,e){let s=this.input.slice(0,this.cursor),i=this.input.slice(this.cursor);this.input=`${s}${t}${i}`,this.cursor=s.length+1,this.complete(this.render),this.render()}delete(){if(0===this.cursor)return this.bell();let t=this.input.slice(0,this.cursor-1),e=this.input.slice(this.cursor);this.input=`${t}${e}`,this.complete(this.render),this.cursor=this.cursor-1,this.render()}deleteForward(){if(this.cursor*this.scale>=this.rendered.length)return this.bell();let t=this.input.slice(0,this.cursor),e=this.input.slice(this.cursor+1);this.input=`${t}${e}`,this.complete(this.render),this.render()}first(){this.moveSelect(0),this.render()}last(){this.moveSelect(this.suggestions.length-1),this.render()}up(){0===this.select?this.moveSelect(this.suggestions.length-1):this.moveSelect(this.select-1),this.render()}down(){this.select===this.suggestions.length-1?this.moveSelect(0):this.moveSelect(this.select+1),this.render()}next(){this.select===this.suggestions.length-1?this.moveSelect(0):this.moveSelect(this.select+1),this.render()}nextPage(){this.moveSelect(Math.min(this.select+this.limit,this.suggestions.length-1)),this.render()}prevPage(){this.moveSelect(Math.max(this.select-this.limit,0)),this.render()}left(){if(this.cursor<=0)return this.bell();this.cursor=this.cursor-1,this.render()}right(){if(this.cursor*this.scale>=this.rendered.length)return this.bell();this.cursor=this.cursor+1,this.render()}renderOption(t,e,s,r){let n,o=s?a.arrowUp:r?a.arrowDown:" ",h=e?i.cyan().underline(t.title):t.title;return o=(e?i.cyan(a.pointer)+" ":"  ")+o,t.description&&(n=` - ${t.description}`,(o.length+h.length+n.length>=this.out.columns||t.description.split(/\r?\n/).length>1)&&(n="\n"+u(t.description,{margin:3,width:this.out.columns}))),o+" "+h+i.gray(n||"")}render(){if(this.closed)return;this.firstRender?this.out.write(o.hide):this.out.write(l(this.outputText,this.out.columns)),super.render();let{startIndex:t,endIndex:e}=d(this.select,this.choices.length,this.limit);if(this.outputText=[h.symbol(this.done,this.aborted,this.exited),i.bold(this.msg),h.delimiter(this.completing),this.done&&this.suggestions[this.select]?this.suggestions[this.select].title:this.rendered=this.transform.render(this.input)].join(" "),!this.done){const s=this.suggestions.slice(t,e).map(((s,i)=>this.renderOption(s,this.select===i+t,0===i&&t>0,i+t===e-1&&e<this.choices.length))).join("\n");this.outputText+="\n"+(s||i.gray(this.fallback.title))}this.out.write(n.line+o.to(0)+this.outputText)}}},function(t,e,s){"use strict";const i=s(0),{cursor:r}=s(1),n=s(7),{clear:o,style:h,figures:l}=s(2);t.exports=class extends n{constructor(t={}){t.overrideRender=!0,super(t),this.inputValue="",this.clear=o("",this.out.columns),this.filteredOptions=this.value,this.render()}last(){this.cursor=this.filteredOptions.length-1,this.render()}next(){this.cursor=(this.cursor+1)%this.filteredOptions.length,this.render()}up(){0===this.cursor?this.cursor=this.filteredOptions.length-1:this.cursor--,this.render()}down(){this.cursor===this.filteredOptions.length-1?this.cursor=0:this.cursor++,this.render()}left(){this.filteredOptions[this.cursor].selected=!1,this.render()}right(){if(this.value.filter((t=>t.selected)).length>=this.maxChoices)return this.bell();this.filteredOptions[this.cursor].selected=!0,this.render()}delete(){this.inputValue.length&&(this.inputValue=this.inputValue.substr(0,this.inputValue.length-1),this.updateFilteredOptions())}updateFilteredOptions(){const t=this.filteredOptions[this.cursor];this.filteredOptions=this.value.filter((t=>!this.inputValue||(!("string"!=typeof t.title||!t.title.toLowerCase().includes(this.inputValue.toLowerCase()))||!("string"!=typeof t.value||!t.value.toLowerCase().includes(this.inputValue.toLowerCase())))));const e=this.filteredOptions.findIndex((e=>e===t));this.cursor=e<0?0:e,this.render()}handleSpaceToggle(){const t=this.filteredOptions[this.cursor];if(t.selected)t.selected=!1,this.render();else{if(t.disabled||this.value.filter((t=>t.selected)).length>=this.maxChoices)return this.bell();t.selected=!0,this.render()}}handleInputChange(t){this.inputValue=this.inputValue+t,this.updateFilteredOptions()}_(t,e){" "===t?this.handleSpaceToggle():this.handleInputChange(t)}renderInstructions(){return void 0===this.instructions||this.instructions?"string"==typeof this.instructions?this.instructions:`\nInstructions:\n    ${l.arrowUp}/${l.arrowDown}: Highlight option\n    ${l.arrowLeft}/${l.arrowRight}/[space]: Toggle selection\n    [a,b,c]/delete: Filter choices\n    enter/return: Complete answer\n`:""}renderCurrentInput(){return`\nFiltered results for: ${this.inputValue?this.inputValue:i.gray("Enter something to filter")}\n`}renderOption(t,e,s){let r;return r=e.disabled?t===s?i.gray().underline(e.title):i.strikethrough().gray(e.title):t===s?i.cyan().underline(e.title):e.title,(e.selected?i.green(l.radioOn):l.radioOff)+"  "+r}renderDoneOrInstructions(){if(this.done)return this.value.filter((t=>t.selected)).map((t=>t.title)).join(", ");const t=[i.gray(this.hint),this.renderInstructions(),this.renderCurrentInput()];return this.filteredOptions.length&&this.filteredOptions[this.cursor].disabled&&t.push(i.yellow(this.warn)),t.join(" ")}render(){if(this.closed)return;this.firstRender&&this.out.write(r.hide),super.render();let t=[h.symbol(this.done,this.aborted),i.bold(this.msg),h.delimiter(!1),this.renderDoneOrInstructions()].join(" ");this.showMinError&&(t+=i.red(`You must select a minimum of ${this.minSelected} choices.`),this.showMinError=!1),t+=this.renderOptions(this.filteredOptions),this.out.write(this.clear+t),this.clear=o(t,this.out.columns)}}},function(t,e,s){const i=s(0),r=s(4),{style:n,clear:o}=s(2),{erase:h,cursor:l}=s(1);t.exports=class extends r{constructor(t={}){super(t),this.msg=t.message,this.value=t.initial,this.initialValue=!!t.initial,this.yesMsg=t.yes||"yes",this.yesOption=t.yesOption||"(Y/n)",this.noMsg=t.no||"no",this.noOption=t.noOption||"(y/N)",this.render()}reset(){this.value=this.initialValue,this.fire(),this.render()}exit(){this.abort()}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write("\n"),this.close()}submit(){this.value=this.value||!1,this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write("\n"),this.close()}_(t,e){return"y"===t.toLowerCase()?(this.value=!0,this.submit()):"n"===t.toLowerCase()?(this.value=!1,this.submit()):this.bell()}render(){this.closed||(this.firstRender?this.out.write(l.hide):this.out.write(o(this.outputText,this.out.columns)),super.render(),this.outputText=[n.symbol(this.done,this.aborted),i.bold(this.msg),n.delimiter(this.done),this.done?this.value?this.yesMsg:this.noMsg:i.gray(this.initialValue?this.yesOption:this.noOption)].join(" "),this.out.write(h.line+l.to(0)+this.outputText))}}}]));