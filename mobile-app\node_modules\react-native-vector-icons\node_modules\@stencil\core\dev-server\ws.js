!function(e,t){for(var s in t)e[s]=t[s]}(exports,function(e){var t={};function s(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,s),r.l=!0,r.exports}return s.m=e,s.c=t,s.d=function(e,t,i){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(s.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)s.d(i,r,function(t){return e[t]}.bind(null,r));return i},s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="",s(s.s=12)}([function(e,t,s){"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),EMPTY_BUFFER:Buffer.alloc(0),NOOP:()=>{}}},function(e,t,s){"use strict";const i=s(18),r=s(2),o=s(19),{kStatusCode:n,NOOP:a}=s(0),c=Buffer.from([0,0,255,255]),h=Symbol("permessage-deflate"),l=Symbol("total-length"),d=Symbol("callback"),f=Symbol("buffers"),u=Symbol("error");let _;function p(e){this[f].push(e),this[l]+=e.length}function m(e){this[l]+=e.length,this[h]._maxPayload<1||this[l]<=this[h]._maxPayload?this[f].push(e):(this[u]=new RangeError("Max payload size exceeded"),this[u][n]=1009,this.removeListener("data",m),this.reset())}function g(e){this[h]._inflate=null,e[n]=1007,this[d](e)}e.exports=class{constructor(e,t,s){if(this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,!_){const e=void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10;_=new o(e)}}static get extensionName(){return"permessage-deflate"}offer(){const e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){const e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){const t=this._options,s=e.find((e=>!(!1===t.serverNoContextTakeover&&e.server_no_context_takeover||e.server_max_window_bits&&(!1===t.serverMaxWindowBits||"number"==typeof t.serverMaxWindowBits&&t.serverMaxWindowBits>e.server_max_window_bits)||"number"==typeof t.clientMaxWindowBits&&!e.client_max_window_bits)));if(!s)throw new Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:!0!==s.client_max_window_bits&&!1!==t.clientMaxWindowBits||delete s.client_max_window_bits,s}acceptAsClient(e){const t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach((e=>{Object.keys(e).forEach((t=>{let s=e[t];if(s.length>1)throw new Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){const e=+s;if(!Number.isInteger(e)||e<8||e>15)throw new TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){const e=+s;if(!Number.isInteger(e)||e<8||e>15)throw new TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else{if("client_no_context_takeover"!==t&&"server_no_context_takeover"!==t)throw new Error(`Unknown parameter "${t}"`);if(!0!==s)throw new TypeError(`Invalid value for parameter "${t}": ${s}`)}e[t]=s}))})),e}decompress(e,t,s){_.add((i=>{this._decompress(e,t,((e,t)=>{i(),s(e,t)}))}))}compress(e,t,s){_.add((i=>{this._compress(e,t,((e,t)=>{i(),s(e,t)}))}))}_decompress(e,t,s){const o=this._isServer?"client":"server";if(!this._inflate){const e=`${o}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=i.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[h]=this,this._inflate[l]=0,this._inflate[f]=[],this._inflate.on("error",g),this._inflate.on("data",m)}this._inflate[d]=s,this._inflate.write(e),t&&this._inflate.write(c),this._inflate.flush((()=>{const e=this._inflate[u];if(e)return this._inflate.close(),this._inflate=null,void s(e);const i=r.concat(this._inflate[f],this._inflate[l]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[l]=0,this._inflate[f]=[],t&&this.params[`${o}_no_context_takeover`]&&this._inflate.reset()),s(null,i)}))}_compress(e,t,s){const o=this._isServer?"server":"client";if(!this._deflate){const e=`${o}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=i.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[l]=0,this._deflate[f]=[],this._deflate.on("error",a),this._deflate.on("data",p)}this._deflate[d]=s,this._deflate.write(e),this._deflate.flush(i.Z_SYNC_FLUSH,(()=>{if(!this._deflate)return;let e=r.concat(this._deflate[f],this._deflate[l]);t&&(e=e.slice(0,e.length-4)),this._deflate[d]=null,this._deflate[l]=0,this._deflate[f]=[],t&&this.params[`${o}_no_context_takeover`]&&this._deflate.reset(),s(null,e)}))}}},function(e,t,s){"use strict";const{EMPTY_BUFFER:i}=s(0);function r(e,t){if(0===e.length)return i;if(1===e.length)return e[0];const s=Buffer.allocUnsafe(t);let r=0;for(let t=0;t<e.length;t++){const i=e[t];s.set(i,r),r+=i.length}return r<t?s.slice(0,r):s}function o(e,t,s,i,r){for(let o=0;o<r;o++)s[i+o]=e[o]^t[3&o]}function n(e,t){const s=e.length;for(let i=0;i<s;i++)e[i]^=t[3&i]}function a(e){return e.byteLength===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)}function c(e){if(c.readOnly=!0,Buffer.isBuffer(e))return e;let t;return e instanceof ArrayBuffer?t=Buffer.from(e):ArrayBuffer.isView(e)?t=Buffer.from(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),c.readOnly=!1),t}try{const t=s(!function(){var e=new Error("Cannot find module 'bufferutil'");throw e.code="MODULE_NOT_FOUND",e}()),i=t.BufferUtil||t;e.exports={concat:r,mask(e,t,s,r,n){n<48?o(e,t,s,r,n):i.mask(e,t,s,r,n)},toArrayBuffer:a,toBuffer:c,unmask(e,t){e.length<32?n(e,t):i.unmask(e,t)}}}catch(t){e.exports={concat:r,mask:o,toArrayBuffer:a,toBuffer:c,unmask:n}}},function(e,t){e.exports=require("crypto")},function(e,t,s){"use strict";const i=s(5),r=s(14),o=s(6),n=s(15),a=s(16),{randomBytes:c,createHash:h}=s(3),{URL:l}=s(17),d=s(1),f=s(7),u=s(10),{BINARY_TYPES:_,EMPTY_BUFFER:p,GUID:m,kStatusCode:g,kWebSocket:y,NOOP:b}=s(0),{addEventListener:v,removeEventListener:w}=s(20),{format:x,parse:S}=s(11),{toBuffer:k}=s(2),E=["CONNECTING","OPEN","CLOSING","CLOSED"],C=[8,13];class O extends i{constructor(e,t,s){super(),this._binaryType=_[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage="",this._closeTimer=null,this._extensions={},this._protocol="",this._readyState=O.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,Array.isArray(t)?t=t.join(", "):"object"==typeof t&&null!==t&&(s=t,t=void 0),N(this,e,t,s)):this._isServer=!0}get binaryType(){return this._binaryType}set binaryType(e){_.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){const i=new f(this.binaryType,this._extensions,this._isServer,s);this._sender=new u(e,this._extensions),this._receiver=i,this._socket=e,i[y]=this,e[y]=this,i.on("conclude",U),i.on("drain",M),i.on("error",I),i.on("message",W),i.on("ping",$),i.on("pong",F),e.setTimeout(0),e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",A),e.on("data",j),e.on("end",D),e.on("error",q),this._readyState=O.OPEN,this.emit("open")}emitClose(){if(!this._socket)return this._readyState=O.CLOSED,void this.emit("close",this._closeCode,this._closeMessage);this._extensions[d.extensionName]&&this._extensions[d.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=O.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==O.CLOSED){if(this.readyState===O.CONNECTING){const e="WebSocket was closed before the connection was established";return B(this,this._req,e)}this.readyState!==O.CLOSING?(this._readyState=O.CLOSING,this._sender.close(e,t,!this._isServer,(e=>{e||(this._closeFrameSent=!0,this._closeFrameReceived&&this._socket.end())})),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)):this._closeFrameSent&&this._closeFrameReceived&&this._socket.end()}}ping(e,t,s){if(this.readyState===O.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");"function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState===O.OPEN?(void 0===t&&(t=!this._isServer),this._sender.ping(e||p,t,s)):P(this,e,s)}pong(e,t,s){if(this.readyState===O.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");"function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState===O.OPEN?(void 0===t&&(t=!this._isServer),this._sender.pong(e||p,t,s)):P(this,e,s)}send(e,t,s){if(this.readyState===O.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==O.OPEN)return void P(this,e,s);const i={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[d.extensionName]||(i.compress=!1),this._sender.send(e||p,i,s)}terminate(){if(this.readyState!==O.CLOSED){if(this.readyState===O.CONNECTING){const e="WebSocket was closed before the connection was established";return B(this,this._req,e)}this._socket&&(this._readyState=O.CLOSING,this._socket.destroy())}}}function N(e,t,s,i){const n={protocolVersion:C[1],maxPayload:104857600,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...i,createConnection:void 0,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:void 0,host:void 0,path:void 0,port:void 0};if(!C.includes(n.protocolVersion))throw new RangeError(`Unsupported protocol version: ${n.protocolVersion} (supported versions: ${C.join(", ")})`);let a;t instanceof l?(a=t,e._url=t.href):(a=new l(t),e._url=t);const f="ws+unix:"===a.protocol;if(!(a.host||f&&a.pathname))throw new Error(`Invalid URL: ${e.url}`);const u="wss:"===a.protocol||"https:"===a.protocol,_=u?443:80,p=c(16).toString("base64"),g=u?r.get:o.get;let y;if(n.createConnection=u?T:L,n.defaultPort=n.defaultPort||_,n.port=a.port||_,n.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,n.headers={"Sec-WebSocket-Version":n.protocolVersion,"Sec-WebSocket-Key":p,Connection:"Upgrade",Upgrade:"websocket",...n.headers},n.path=a.pathname+a.search,n.timeout=n.handshakeTimeout,n.perMessageDeflate&&(y=new d(!0!==n.perMessageDeflate?n.perMessageDeflate:{},!1,n.maxPayload),n.headers["Sec-WebSocket-Extensions"]=x({[d.extensionName]:y.offer()})),s&&(n.headers["Sec-WebSocket-Protocol"]=s),n.origin&&(n.protocolVersion<13?n.headers["Sec-WebSocket-Origin"]=n.origin:n.headers.Origin=n.origin),(a.username||a.password)&&(n.auth=`${a.username}:${a.password}`),f){const e=n.path.split(":");n.socketPath=e[0],n.path=e[1]}let b=e._req=g(n);n.timeout&&b.on("timeout",(()=>{B(e,b,"Opening handshake has timed out")})),b.on("error",(t=>{null===b||b.aborted||(b=e._req=null,e._readyState=O.CLOSING,e.emit("error",t),e.emitClose())})),b.on("response",(r=>{const o=r.headers.location,a=r.statusCode;if(o&&n.followRedirects&&a>=300&&a<400){if(++e._redirects>n.maxRedirects)return void B(e,b,"Maximum redirects exceeded");b.abort();const r=new l(o,t);N(e,r,s,i)}else e.emit("unexpected-response",b,r)||B(e,b,`Unexpected server response: ${r.statusCode}`)})),b.on("upgrade",((t,i,r)=>{if(e.emit("upgrade",t),e.readyState!==O.CONNECTING)return;b=e._req=null;const o=h("sha1").update(p+m).digest("base64");if(t.headers["sec-websocket-accept"]!==o)return void B(e,i,"Invalid Sec-WebSocket-Accept header");const a=t.headers["sec-websocket-protocol"],c=(s||"").split(/, */);let l;if(!s&&a?l="Server sent a subprotocol but none was requested":s&&!a?l="Server sent no subprotocol":a&&!c.includes(a)&&(l="Server sent an invalid subprotocol"),l)B(e,i,l);else{if(a&&(e._protocol=a),y)try{const s=S(t.headers["sec-websocket-extensions"]);s[d.extensionName]&&(y.accept(s[d.extensionName]),e._extensions[d.extensionName]=y)}catch(t){return void B(e,i,"Invalid Sec-WebSocket-Extensions header")}e.setSocket(i,r,n.maxPayload)}}))}function L(e){return e.path=e.socketPath,n.connect(e)}function T(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=n.isIP(e.host)?"":e.host),a.connect(e)}function B(e,t,s){e._readyState=O.CLOSING;const i=new Error(s);Error.captureStackTrace(i,B),t.setHeader?(t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),t.once("abort",e.emitClose.bind(e)),e.emit("error",i)):(t.destroy(i),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function P(e,t,s){if(t){const s=k(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){s(new Error(`WebSocket is not open: readyState ${e.readyState} (${E[e.readyState]})`))}}function U(e,t){const s=this[y];s._socket.removeListener("data",j),s._socket.resume(),s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,1005===e?s.close():s.close(e,t)}function M(){this[y]._socket.resume()}function I(e){const t=this[y];t._socket.removeListener("data",j),t._readyState=O.CLOSING,t._closeCode=e[g],t.emit("error",e),t._socket.destroy()}function R(){this[y].emitClose()}function W(e){this[y].emit("message",e)}function $(e){const t=this[y];t.pong(e,!t._isServer,b),t.emit("ping",e)}function F(e){this[y].emit("pong",e)}function A(){const e=this[y];this.removeListener("close",A),this.removeListener("end",D),e._readyState=O.CLOSING,e._socket.read(),e._receiver.end(),this.removeListener("data",j),this[y]=void 0,clearTimeout(e._closeTimer),e._receiver._writableState.finished||e._receiver._writableState.errorEmitted?e.emitClose():(e._receiver.on("error",R),e._receiver.on("finish",R))}function j(e){this[y]._receiver.write(e)||this.pause()}function D(){const e=this[y];e._readyState=O.CLOSING,e._receiver.end(),this.end()}function q(){const e=this[y];this.removeListener("error",q),this.on("error",b),e&&(e._readyState=O.CLOSING,this.destroy())}E.forEach(((e,t)=>{const s={enumerable:!0,value:t};Object.defineProperty(O.prototype,e,s),Object.defineProperty(O,e,s)})),["binaryType","bufferedAmount","extensions","protocol","readyState","url"].forEach((e=>{Object.defineProperty(O.prototype,e,{enumerable:!0})})),["open","error","close","message"].forEach((e=>{Object.defineProperty(O.prototype,`on${e}`,{configurable:!0,enumerable:!0,get(){const t=this.listeners(e);for(let e=0;e<t.length;e++)if(t[e]._listener)return t[e]._listener},set(t){const s=this.listeners(e);for(let t=0;t<s.length;t++)s[t]._listener&&this.removeListener(e,s[t]);this.addEventListener(e,t)}})})),O.prototype.addEventListener=v,O.prototype.removeEventListener=w,e.exports=O},function(e,t){e.exports=require("events")},function(e,t){e.exports=require("http")},function(e,t,s){"use strict";const{Writable:i}=s(8),r=s(1),{BINARY_TYPES:o,EMPTY_BUFFER:n,kStatusCode:a,kWebSocket:c}=s(0),{concat:h,toArrayBuffer:l,unmask:d}=s(2),{isValidStatusCode:f,isValidUTF8:u}=s(9);function _(e,t,s,i){const r=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(r,_),r[a]=i,r}e.exports=class extends i{constructor(e,t,s,i){super(),this._binaryType=e||o[0],this[c]=void 0,this._extensions=t||{},this._isServer=!!s,this._maxPayload=0|i,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._state=0,this._loop=!1}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){const t=this._buffers[0];return this._buffers[0]=t.slice(e),t.slice(0,e)}const t=Buffer.allocUnsafe(e);do{const s=this._buffers[0],i=t.length-e;e>=s.length?t.set(this._buffers.shift(),i):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),i),this._buffers[0]=s.slice(e)),e-=s.length}while(e>0);return t}startLoop(e){let t;this._loop=!0;do{switch(this._state){case 0:t=this.getInfo();break;case 1:t=this.getPayloadLength16();break;case 2:t=this.getPayloadLength64();break;case 3:this.getMask();break;case 4:t=this.getData(e);break;default:return void(this._loop=!1)}}while(this._loop);e(t)}getInfo(){if(this._bufferedBytes<2)return void(this._loop=!1);const e=this.consume(2);if(0!=(48&e[0]))return this._loop=!1,_(RangeError,"RSV2 and RSV3 must be clear",!0,1002);const t=64==(64&e[0]);if(t&&!this._extensions[r.extensionName])return this._loop=!1,_(RangeError,"RSV1 must be clear",!0,1002);if(this._fin=128==(128&e[0]),this._opcode=15&e[0],this._payloadLength=127&e[1],0===this._opcode){if(t)return this._loop=!1,_(RangeError,"RSV1 must be clear",!0,1002);if(!this._fragmented)return this._loop=!1,_(RangeError,"invalid opcode 0",!0,1002);this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return this._loop=!1,_(RangeError,`invalid opcode ${this._opcode}`,!0,1002);this._compressed=t}else{if(!(this._opcode>7&&this._opcode<11))return this._loop=!1,_(RangeError,`invalid opcode ${this._opcode}`,!0,1002);if(!this._fin)return this._loop=!1,_(RangeError,"FIN must be set",!0,1002);if(t)return this._loop=!1,_(RangeError,"RSV1 must be clear",!0,1002);if(this._payloadLength>125)return this._loop=!1,_(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002)}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=128==(128&e[1]),this._isServer){if(!this._masked)return this._loop=!1,_(RangeError,"MASK must be set",!0,1002)}else if(this._masked)return this._loop=!1,_(RangeError,"MASK must be clear",!0,1002);if(126===this._payloadLength)this._state=1;else{if(127!==this._payloadLength)return this.haveLength();this._state=2}}getPayloadLength16(){if(!(this._bufferedBytes<2))return this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength();this._loop=!1}getPayloadLength64(){if(this._bufferedBytes<8)return void(this._loop=!1);const e=this.consume(8),t=e.readUInt32BE(0);return t>Math.pow(2,21)-1?(this._loop=!1,_(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009)):(this._payloadLength=t*Math.pow(2,32)+e.readUInt32BE(4),this.haveLength())}haveLength(){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return this._loop=!1,_(RangeError,"Max payload size exceeded",!1,1009);this._masked?this._state=3:this._state=4}getMask(){this._bufferedBytes<4?this._loop=!1:(this._mask=this.consume(4),this._state=4)}getData(e){let t=n;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength)return void(this._loop=!1);t=this.consume(this._payloadLength),this._masked&&d(t,this._mask)}return this._opcode>7?this.controlMessage(t):this._compressed?(this._state=5,void this.decompress(t,e)):(t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage())}decompress(e,t){this._extensions[r.extensionName].decompress(e,this._fin,((e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return t(_(RangeError,"Max payload size exceeded",!1,1009));this._fragments.push(s)}const i=this.dataMessage();if(i)return t(i);this.startLoop(t)}))}dataMessage(){if(this._fin){const e=this._messageLength,t=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let s;s="nodebuffer"===this._binaryType?h(t,e):"arraybuffer"===this._binaryType?l(h(t,e)):t,this.emit("message",s)}else{const s=h(t,e);if(!u(s))return this._loop=!1,_(Error,"invalid UTF-8 sequence",!0,1007);this.emit("message",s.toString())}}this._state=0}controlMessage(e){if(8===this._opcode)if(this._loop=!1,0===e.length)this.emit("conclude",1005,""),this.end();else{if(1===e.length)return _(RangeError,"invalid payload length 1",!0,1002);{const t=e.readUInt16BE(0);if(!f(t))return _(RangeError,`invalid status code ${t}`,!0,1002);const s=e.slice(2);if(!u(s))return _(Error,"invalid UTF-8 sequence",!0,1007);this.emit("conclude",t,s.toString()),this.end()}}else 9===this._opcode?this.emit("ping",e):this.emit("pong",e);this._state=0}}},function(e,t){e.exports=require("stream")},function(e,t,s){"use strict";function i(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999}function r(e){const t=e.length;let s=0;for(;s<t;)if(0==(128&e[s]))s++;else if(192==(224&e[s])){if(s+1===t||128!=(192&e[s+1])||192==(254&e[s]))return!1;s+=2}else if(224==(240&e[s])){if(s+2>=t||128!=(192&e[s+1])||128!=(192&e[s+2])||224===e[s]&&128==(224&e[s+1])||237===e[s]&&160==(224&e[s+1]))return!1;s+=3}else{if(240!=(248&e[s]))return!1;if(s+3>=t||128!=(192&e[s+1])||128!=(192&e[s+2])||128!=(192&e[s+3])||240===e[s]&&128==(240&e[s+1])||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}try{let t=s(!function(){var e=new Error("Cannot find module 'utf-8-validate'");throw e.code="MODULE_NOT_FOUND",e}());"object"==typeof t&&(t=t.Validation.isValidUTF8),e.exports={isValidStatusCode:i,isValidUTF8:e=>e.length<150?r(e):t(e)}}catch(t){e.exports={isValidStatusCode:i,isValidUTF8:r}}},function(e,t,s){"use strict";const{randomFillSync:i}=s(3),r=s(1),{EMPTY_BUFFER:o}=s(0),{isValidStatusCode:n}=s(9),{mask:a,toBuffer:c}=s(2),h=Buffer.alloc(4);class l{constructor(e,t){this._extensions=t||{},this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){const s=t.mask&&t.readOnly;let r=t.mask?6:2,o=e.length;e.length>=65536?(r+=8,o=127):e.length>125&&(r+=2,o=126);const n=Buffer.allocUnsafe(s?e.length+r:r);return n[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(n[0]|=64),n[1]=o,126===o?n.writeUInt16BE(e.length,2):127===o&&(n.writeUInt32BE(0,2),n.writeUInt32BE(e.length,6)),t.mask?(i(h,0,4),n[1]|=128,n[r-4]=h[0],n[r-3]=h[1],n[r-2]=h[2],n[r-1]=h[3],s?(a(e,h,n,r,e.length),[n]):(a(e,h,e,0,e.length),[n,e])):[n,e]}close(e,t,s,i){let r;if(void 0===e)r=o;else{if("number"!=typeof e||!n(e))throw new TypeError("First argument must be a valid error code number");if(void 0===t||""===t)r=Buffer.allocUnsafe(2),r.writeUInt16BE(e,0);else{const s=Buffer.byteLength(t);if(s>123)throw new RangeError("The message must not be greater than 123 bytes");r=Buffer.allocUnsafe(2+s),r.writeUInt16BE(e,0),r.write(t,2)}}this._deflating?this.enqueue([this.doClose,r,s,i]):this.doClose(r,s,i)}doClose(e,t,s){this.sendFrame(l.frame(e,{fin:!0,rsv1:!1,opcode:8,mask:t,readOnly:!1}),s)}ping(e,t,s){const i=c(e);if(i.length>125)throw new RangeError("The data size must not be greater than 125 bytes");this._deflating?this.enqueue([this.doPing,i,t,c.readOnly,s]):this.doPing(i,t,c.readOnly,s)}doPing(e,t,s,i){this.sendFrame(l.frame(e,{fin:!0,rsv1:!1,opcode:9,mask:t,readOnly:s}),i)}pong(e,t,s){const i=c(e);if(i.length>125)throw new RangeError("The data size must not be greater than 125 bytes");this._deflating?this.enqueue([this.doPong,i,t,c.readOnly,s]):this.doPong(i,t,c.readOnly,s)}doPong(e,t,s,i){this.sendFrame(l.frame(e,{fin:!0,rsv1:!1,opcode:10,mask:t,readOnly:s}),i)}send(e,t,s){const i=c(e),o=this._extensions[r.extensionName];let n=t.binary?2:1,a=t.compress;if(this._firstFragment?(this._firstFragment=!1,a&&o&&(a=i.length>=o._threshold),this._compress=a):(a=!1,n=0),t.fin&&(this._firstFragment=!0),o){const e={fin:t.fin,rsv1:a,opcode:n,mask:t.mask,readOnly:c.readOnly};this._deflating?this.enqueue([this.dispatch,i,this._compress,e,s]):this.dispatch(i,this._compress,e,s)}else this.sendFrame(l.frame(i,{fin:t.fin,rsv1:!1,opcode:n,mask:t.mask,readOnly:c.readOnly}),s)}dispatch(e,t,s,i){if(!t)return void this.sendFrame(l.frame(e,s),i);const o=this._extensions[r.extensionName];this._bufferedBytes+=e.length,this._deflating=!0,o.compress(e,s.fin,((t,r)=>{if(this._socket.destroyed){const e=new Error("The socket was closed while data was being compressed");"function"==typeof i&&i(e);for(let t=0;t<this._queue.length;t++){const s=this._queue[t][4];"function"==typeof s&&s(e)}}else this._bufferedBytes-=e.length,this._deflating=!1,s.readOnly=!1,this.sendFrame(l.frame(r,s),i),this.dequeue()}))}dequeue(){for(;!this._deflating&&this._queue.length;){const e=this._queue.shift();this._bufferedBytes-=e[1].length,Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[1].length,this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=l},function(e,t,s){"use strict";const i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function r(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map((t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map((e=>[t].concat(Object.keys(e).map((t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map((e=>!0===e?t:`${t}=${e}`)).join("; ")}))).join("; "))).join(", ")})).join(", ")},parse:function(e){const t=Object.create(null);if(void 0===e||""===e)return t;let s,o,n=Object.create(null),a=!1,c=!1,h=!1,l=-1,d=-1,f=0;for(;f<e.length;f++){const u=e.charCodeAt(f);if(void 0===s)if(-1===d&&1===i[u])-1===l&&(l=f);else if(32===u||9===u)-1===d&&-1!==l&&(d=f);else{if(59!==u&&44!==u)throw new SyntaxError(`Unexpected character at index ${f}`);{if(-1===l)throw new SyntaxError(`Unexpected character at index ${f}`);-1===d&&(d=f);const i=e.slice(l,d);44===u?(r(t,i,n),n=Object.create(null)):s=i,l=d=-1}}else if(void 0===o)if(-1===d&&1===i[u])-1===l&&(l=f);else if(32===u||9===u)-1===d&&-1!==l&&(d=f);else if(59===u||44===u){if(-1===l)throw new SyntaxError(`Unexpected character at index ${f}`);-1===d&&(d=f),r(n,e.slice(l,d),!0),44===u&&(r(t,s,n),n=Object.create(null),s=void 0),l=d=-1}else{if(61!==u||-1===l||-1!==d)throw new SyntaxError(`Unexpected character at index ${f}`);o=e.slice(l,f),l=d=-1}else if(c){if(1!==i[u])throw new SyntaxError(`Unexpected character at index ${f}`);-1===l?l=f:a||(a=!0),c=!1}else if(h)if(1===i[u])-1===l&&(l=f);else if(34===u&&-1!==l)h=!1,d=f;else{if(92!==u)throw new SyntaxError(`Unexpected character at index ${f}`);c=!0}else if(34===u&&61===e.charCodeAt(f-1))h=!0;else if(-1===d&&1===i[u])-1===l&&(l=f);else if(-1===l||32!==u&&9!==u){if(59!==u&&44!==u)throw new SyntaxError(`Unexpected character at index ${f}`);{if(-1===l)throw new SyntaxError(`Unexpected character at index ${f}`);-1===d&&(d=f);let i=e.slice(l,d);a&&(i=i.replace(/\\/g,""),a=!1),r(n,o,i),44===u&&(r(t,s,n),n=Object.create(null),s=void 0),o=void 0,l=d=-1}}else-1===d&&(d=f)}if(-1===l||h)throw new SyntaxError("Unexpected end of input");-1===d&&(d=f);const u=e.slice(l,d);return void 0===s?r(t,u,n):(void 0===o?r(n,u,!0):r(n,o,a?u.replace(/\\/g,""):u),r(t,s,n)),t}}},function(e,t,s){var i=s(13);t.Server=i.Server,t.on=i.on,t.close=i.close,t.ping=i.ping},function(e,t,s){"use strict";const i=s(4);i.createWebSocketStream=s(21),i.Server=s(22),i.Receiver=s(7),i.Sender=s(10),e.exports=i},function(e,t){e.exports=require("https")},function(e,t){e.exports=require("net")},function(e,t){e.exports=require("tls")},function(e,t){e.exports=require("url")},function(e,t){e.exports=require("zlib")},function(e,t,s){"use strict";const i=Symbol("kDone"),r=Symbol("kRun");e.exports=class{constructor(e){this[i]=()=>{this.pending--,this[r]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[r]()}[r](){if(this.pending!==this.concurrency&&this.jobs.length){const e=this.jobs.shift();this.pending++,e(this[i])}}}},function(e,t,s){"use strict";class i{constructor(e,t){this.target=t,this.type=e}}class r extends i{constructor(e,t){super("message",t),this.data=e}}class o extends i{constructor(e,t,s){super("close",s),this.wasClean=s._closeFrameReceived&&s._closeFrameSent,this.reason=t,this.code=e}}class n extends i{constructor(e){super("open",e)}}class a extends i{constructor(e,t){super("error",t),this.message=e.message,this.error=e}}const c={addEventListener(e,t,s){if("function"!=typeof t)return;function i(e){t.call(this,new r(e,this))}function c(e,s){t.call(this,new o(e,s,this))}function h(e){t.call(this,new a(e,this))}function l(){t.call(this,new n(this))}const d=s&&s.once?"once":"on";"message"===e?(i._listener=t,this[d](e,i)):"close"===e?(c._listener=t,this[d](e,c)):"error"===e?(h._listener=t,this[d](e,h)):"open"===e?(l._listener=t,this[d](e,l)):this[d](e,t)},removeEventListener(e,t){const s=this.listeners(e);for(let i=0;i<s.length;i++)s[i]!==t&&s[i]._listener!==t||this.removeListener(e,s[i])}};e.exports=c},function(e,t,s){"use strict";const{Duplex:i}=s(8);function r(e){e.emit("close")}function o(){!this.destroyed&&this._writableState.finished&&this.destroy()}function n(e){this.removeListener("error",n),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0;function a(){s&&e._socket.resume()}e.readyState===e.CONNECTING?e.once("open",(function(){e._receiver.removeAllListeners("drain"),e._receiver.on("drain",a)})):(e._receiver.removeAllListeners("drain"),e._receiver.on("drain",a));const c=new i({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",(function(t){c.push(t)||(s=!1,e._socket.pause())})),e.once("error",(function(e){c.destroyed||c.destroy(e)})),e.once("close",(function(){c.destroyed||c.push(null)})),c._destroy=function(t,s){if(e.readyState===e.CLOSED)return s(t),void process.nextTick(r,c);let i=!1;e.once("error",(function(e){i=!0,s(e)})),e.once("close",(function(){i||s(t),process.nextTick(r,c)})),e.terminate()},c._final=function(t){e.readyState!==e.CONNECTING?null!==e._socket&&(e._socket._writableState.finished?(t(),c._readableState.endEmitted&&c.destroy()):(e._socket.once("finish",(function(){t()})),e.close())):e.once("open",(function(){c._final(t)}))},c._read=function(){e.readyState!==e.OPEN||s||(s=!0,e._receiver._writableState.needDrain||e._socket.resume())},c._write=function(t,s,i){e.readyState!==e.CONNECTING?e.send(t,i):e.once("open",(function(){c._write(t,s,i)}))},c.on("end",o),c.on("error",n),c}},function(e,t,s){"use strict";const i=s(5),{createHash:r}=s(3),{createServer:o,STATUS_CODES:n}=s(6),a=s(1),c=s(4),{format:h,parse:l}=s(11),{GUID:d,kWebSocket:f}=s(0),u=/^[+/0-9A-Za-z]{22}==$/;function _(e){e.emit("close")}function p(){this.destroy()}function m(e,t,s,i){e.writable&&(s=s||n[t],i={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...i},e.write(`HTTP/1.1 ${t} ${n[t]}\r\n`+Object.keys(i).map((e=>`${e}: ${i[e]}`)).join("\r\n")+"\r\n\r\n"+s)),e.removeListener("error",p),e.destroy()}function g(e){return e.trim()}e.exports=class extends i{constructor(e,t){if(super(),null==(e={maxPayload:104857600,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,...e}).port&&!e.server&&!e.noServer)throw new TypeError('One of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=o(((e,t)=>{const s=n[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)})),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){const e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(const s of Object.keys(t))e.on(s,t[s]);return function(){for(const s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,i)=>{this.handleUpgrade(t,s,i,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set),this.options=e}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(e&&this.once("close",e),this.clients)for(const e of this.clients)e.terminate();const t=this._server;t&&(this._removeListeners(),this._removeListeners=this._server=null,null!=this.options.port)?t.close((()=>this.emit("close"))):process.nextTick(_,this)}shouldHandle(e){if(this.options.path){const t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,i){t.on("error",p);const r=void 0!==e.headers["sec-websocket-key"]&&e.headers["sec-websocket-key"].trim(),o=+e.headers["sec-websocket-version"],n={};if("GET"!==e.method||"websocket"!==e.headers.upgrade.toLowerCase()||!r||!u.test(r)||8!==o&&13!==o||!this.shouldHandle(e))return m(t,400);if(this.options.perMessageDeflate){const s=new a(this.options.perMessageDeflate,!0,this.options.maxPayload);try{const t=l(e.headers["sec-websocket-extensions"]);t[a.extensionName]&&(s.accept(t[a.extensionName]),n[a.extensionName]=s)}catch(e){return m(t,400)}}if(this.options.verifyClient){const a={origin:e.headers[""+(8===o?"sec-websocket-origin":"origin")],secure:!(!e.socket.authorized&&!e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(a,((o,a,c,h)=>{if(!o)return m(t,a||401,c,h);this.completeUpgrade(r,n,e,t,s,i)}));if(!this.options.verifyClient(a))return m(t,401)}this.completeUpgrade(r,n,e,t,s,i)}completeUpgrade(e,t,s,i,o,n){if(!i.readable||!i.writable)return i.destroy();if(i[f])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");const l=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${r("sha1").update(e+d).digest("base64")}`],u=new c(null);let _=s.headers["sec-websocket-protocol"];if(_&&(_=_.split(",").map(g),_=this.options.handleProtocols?this.options.handleProtocols(_,s):_[0],_&&(l.push(`Sec-WebSocket-Protocol: ${_}`),u._protocol=_)),t[a.extensionName]){const e=t[a.extensionName].params,s=h({[a.extensionName]:[e]});l.push(`Sec-WebSocket-Extensions: ${s}`),u._extensions=t}this.emit("headers",l,s),i.write(l.concat("\r\n").join("\r\n")),i.removeListener("error",p),u.setSocket(i,o,this.options.maxPayload),this.clients&&(this.clients.add(u),u.on("close",(()=>this.clients.delete(u)))),n(u,s)}}}]));