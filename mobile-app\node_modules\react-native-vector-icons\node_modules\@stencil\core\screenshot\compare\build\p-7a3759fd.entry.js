import{r as e,d as t,h as i}from"./p-fbbae598.js";class s{constructor(i){e(this,i),this.filterChange=t(this,"filterChange",7)}render(){if(!this.diffs||0===this.diffs.length||!this.filter)return;const e=this.diffs.reduce((e,t)=>(e.some(e=>e.value===t.device)||e.push({text:t.device,value:t.device}),e),[{text:"All Devices",value:""}]);return i("section",null,i("div",{class:"showing"},"Showing ",this.diffs.filter(e=>e.show).length),i("div",{class:"search"},i("input",{type:"search",onInput:e=>{this.filterChange.emit({search:e.target.value})},value:this.filter.search||""})),e.length>1?i("div",{class:"device"},i("select",{onInput:e=>{this.filterChange.emit({device:e.target.value})}},e.map(e=>i("option",{key:e.value,selected:e.value===this.filter.device,value:e.value},e.text)))):null,i("div",{class:"mismatch"},i("select",{onInput:e=>{this.filterChange.emit({mismatch:e.target.value})}},i("option",{value:"",selected:""===this.filter.mismatch},"> 0"),i("option",{value:"100",selected:"100"===this.filter.mismatch},"> 100"),i("option",{value:"250",selected:"250"===this.filter.mismatch},"> 250"),i("option",{value:"500",selected:"500"===this.filter.mismatch},"> 500"),i("option",{value:"1000",selected:"1000"===this.filter.mismatch},"> 1,000"),i("option",{value:"2500",selected:"2500"===this.filter.mismatch},"> 2,500"),i("option",{value:"5000",selected:"5000"===this.filter.mismatch},"> 5,000"),i("option",{value:"10000",selected:"10000"===this.filter.mismatch},"> 10,000"),i("option",{value:"25000",selected:"25000"===this.filter.mismatch},"> 25,000"),i("option",{value:"50000",selected:"50000"===this.filter.mismatch},"> 50,000"),i("option",{value:"all",selected:"all"===this.filter.mismatch},"All Screenshots"))))}}s.style="select{font-size:10px}input{font-size:10px}.showing{font-size:12px;white-space:nowrap;margin:17px 8px 0 0;color:var(--analysis-data-color)}section{display:flex;justify-content:flex-end}.search{margin:13px 8px 0 0}.device{margin:13px 8px 0 0}.mismatch{margin:13px 8px 0 0}";class a{constructor(t){e(this,t)}render(){return[i("header",null,i("div",{class:"logo"},i("a",{href:"/"},i("img",{src:this.appSrcUrl+"/assets/logo.png?1"}))),i("compare-filter",{diffs:this.diffs,filter:this.filter}))]}}a.style=":host{background:white;box-shadow:var(--header-box-shadow)}nav{padding:4px 4px}nav a{font-size:14px;text-decoration:none;color:var(--breadcrumb-color);display:inline-block;padding:0 4px 0 4px}nav a:hover{text-decoration:underline}header{display:flex;width:calc(100% - 115px);padding:8px}img{width:174px;height:32px}.logo{flex:1;padding:7px}compare-filter{flex:1}h1{margin:0;padding:0;font-size:18px}";export{s as compare_filter,a as compare_header}