{"name": "@stencil/core", "version": "2.10.0", "license": "MIT", "main": "./internal/stencil-core/index.cjs", "module": "./internal/stencil-core/index.js", "types": "./internal/stencil-core/index.d.ts", "bin": {"stencil": "bin/stencil"}, "files": ["!**/*.map", "bin/", "cli/", "compiler/", "dependencies.json", "dev-server/", "internal/", "mock-doc/", "screenshot/", "sys/", "testing/"], "scripts": {"clean": "rm -rf build/ cli/ compiler/ dev-server/ internal/ mock-doc/ sys/ testing/ && npm run clean-scripts", "clean-scripts": "rm -rf scripts/build", "start": "npm run watch", "build": "node scripts --prepare && npm run tsc.prod && npm run rollup.prod.ci", "watch": "node scripts && npm run tsc && concurrently \"npm run rollup.watch\" \"npm run tsc.watch\"", "release": "node scripts --release --publish", "release.prepare": "node scripts --release --prepare", "tsc": "tsc --incremental", "tsc.prod": "tsc", "tsc.scripts": "tsc -p scripts/tsconfig.json", "tsc.watch": "tsc --incremental --watch", "rollup": "rollup --config", "rollup.prod": "rollup --config --config-prod", "rollup.prod.ci": "rollup --config --config-prod --config-ci", "rollup.watch": "rollup --watch --config", "license": "node scripts --license", "prettier": "npm run prettier.base -- --write", "prettier.base": "prettier \"./{bin,scripts,src,test}/**/*.{ts,tsx,js,jsx}\"", "prettier.dry-run": "npm run prettier.base -- --list-different", "test": "jest --coverage", "test.analysis": "cd test && npm run build", "test.dist": "node scripts --validate-build", "test.end-to-end": "cd test/end-to-end && npm ci && npm test && npm run test.dist", "test.jest": "jest", "test.karma": "cd test/karma && npm ci && npm run karma", "test.karma.prod": "cd test/karma && npm ci && npm run karma.prod", "test.testing": "node scripts/test/validate-testing.js", "test.sys.node": "echo test.sys.node", "test.prod": "npm run test.dist && npm run test.end-to-end && npm run test.jest && npm run test.karma && npm run test.sys.node && npm run test.testing", "test.watch": "jest --watch", "test.watch-all": "jest --watchAll --coverage"}, "devDependencies": {"@ionic/prettier-config": "^2.0.0", "@rollup/plugin-commonjs": "15.1.0", "@rollup/plugin-json": "4.1.0", "@rollup/plugin-node-resolve": "9.0.0", "@rollup/plugin-replace": "2.3.4", "@rollup/pluginutils": "4.1.0", "@types/autoprefixer": "^10.2.0", "@types/exit": "^0.1.31", "@types/fs-extra": "^9.0.8", "@types/glob": "^7.1.2", "@types/graceful-fs": "^4.1.5", "@types/inquirer": "^7.3.1", "@types/is-glob": "^4.0.1", "@types/jest": "^26.0.21", "@types/listr": "^0.14.2", "@types/mime-types": "^2.1.0", "@types/node": "^14.14.35", "@types/node-fetch": "^2.5.8", "@types/parse5": "^6.0.0", "@types/pixelmatch": "^4.0.0", "@types/pngjs": "^3.4.2", "@types/prompts": "^2.0.9", "@types/semver": "^7.3.4", "@types/sizzle": "^2.3.2", "@types/webpack": "^4.41.26", "@types/ws": "^7.4.0", "@types/yarnpkg__lockfile": "^1.1.5", "@yarnpkg/lockfile": "^1.1.0", "ansi-colors": "4.1.1", "autoprefixer": "10.2.5", "concurrently": "^6.0.0", "conventional-changelog-cli": "^2.1.1", "core-js-builder": "~3.6.5", "css": "^3.0.0", "dts-bundle-generator": "~5.3.0", "execa": "4.1.0", "exit": "^0.1.2", "fast-deep-equal": "3.1.3", "fs-extra": "^9.1.0", "glob": "7.1.6", "graceful-fs": "~4.2.6", "hash.js": "^1.1.7", "inquirer": "^7.3.3", "jest": "^26.6.3", "jest-cli": "^26.6.3", "jest-environment-node": "^26.6.2", "listr": "^0.14.3", "magic-string": "^0.25.7", "merge-source-map": "^1.1.0", "mime-db": "^1.46.0", "minimatch": "3.0.4", "node-fetch": "2.6.1", "open": "8.2.1", "open-in-editor": "2.2.0", "parse5": "6.0.1", "path-browserify": "^1.0.1", "pixelmatch": "4.0.2", "postcss": "^8.2.8", "prettier": "2.4.0", "prompts": "2.4.0", "puppeteer": "~10.0.0", "rollup": "2.42.3", "rollup-plugin-sourcemaps": "^0.6.3", "semiver": "^1.1.0", "semver": "7.3.4", "sizzle": "^2.3.6", "terser": "5.6.1", "tslib": "^2.1.0", "typescript": "4.3.5", "webpack": "^4.46.0", "ws": "7.4.6"}, "engines": {"node": ">=12.10.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/ionic-team/stencil.git"}, "author": "Ionic Team", "homepage": "https://stenciljs.com/", "description": "A Compiler for Web Components and Progressive Web Apps", "keywords": ["web components", "components", "stencil", "ionic", "webapp", "custom elements", "pwa", "progressive web app"], "prettier": "@ionic/prettier-config"}