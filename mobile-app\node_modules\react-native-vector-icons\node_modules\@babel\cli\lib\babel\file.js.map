{"version": 3, "names": ["_convertSourceMap", "data", "require", "_traceMapping", "_slash", "_path", "_fs", "util", "watcher", "asyncGeneratorStep", "n", "t", "e", "r", "o", "a", "c", "i", "u", "value", "done", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_default", "_x", "_ref", "cliOptions", "babelOptions", "buildResult", "fileResults", "mapSections", "code", "offset", "hasRawMap", "result", "map", "push", "line", "column", "version", "names", "sources", "mappings", "countNewlines", "AnyMap", "file", "sourceMapTarget", "path", "basename", "outFile", "sections", "sourceRoot", "sourceMaps", "convertSourceMap", "fromObject", "encodedMap", "toComment", "count", "index", "indexOf", "output", "v", "w", "split", "process", "versions", "node", "mkdirSync", "sync", "dirname", "recursive", "outputMap", "hasDataSourcemap", "mapLoc", "addSourceMappingUrl", "fs", "writeFileSync", "JSON", "stringify", "stdout", "write", "readStdin", "reject", "stdin", "setEncoding", "on", "chunk", "read", "_stdin", "res", "transformRepl", "filename", "Object", "assign", "sourceFileName", "walk", "_x2", "_walk", "filenames", "_filenames", "for<PERSON>ach", "existsSync", "stat", "statSync", "isDirectory", "readdirForCompilable", "includeDotfiles", "extensions", "results", "all", "sourceFilename", "relative", "slash", "compile", "err", "watch", "console", "error", "files", "_x3", "_files", "enable", "enableGlobbing", "skipInitialBuild", "startWatcher", "onFilesChange", "changes", "event", "cause", "actionableChange", "some", "isCompilableExtension", "includes", "verbose", "log", "catch", "length"], "sources": ["../../src/babel/file.ts"], "sourcesContent": ["import convertSourceMap from \"convert-source-map\";\nimport { AnyMap, encodedMap } from \"@jridgewell/trace-mapping\";\nimport slash from \"slash\";\nimport path from \"node:path\";\nimport fs from \"node:fs\";\n\nimport * as util from \"./util.ts\";\nimport type { CmdOptions } from \"./options.ts\";\nimport * as watcher from \"./watcher.ts\";\n\nimport type {\n  EncodedSourceMap,\n  SectionedSourceMap,\n  SourceMapInput,\n  TraceMap,\n} from \"@jridgewell/trace-mapping\";\nimport type { FileResult } from \"@babel/core\";\n\ntype CompilationOutput = {\n  code: string;\n  map: SourceMapInput;\n  hasRawMap: boolean;\n};\n\nexport default async function ({\n  cliOptions,\n  babelOptions,\n}: CmdOptions): Promise<void> {\n  function buildResult(fileResults: Array<FileResult>): CompilationOutput {\n    const mapSections: SectionedSourceMap[\"sections\"] = [];\n\n    let code = \"\";\n    let offset = 0;\n\n    let hasRawMap = false;\n\n    for (const result of fileResults) {\n      if (!result) continue;\n\n      hasRawMap = !!result.map;\n\n      mapSections.push({\n        offset: { line: offset, column: 0 },\n        map: (result.map as EncodedSourceMap) || {\n          version: 3,\n          names: [],\n          sources: [],\n          mappings: [],\n        },\n      });\n\n      code += result.code + \"\\n\";\n      offset += countNewlines(result.code) + 1;\n    }\n\n    const map = new AnyMap({\n      version: 3,\n      file:\n        cliOptions.sourceMapTarget ||\n        path.basename(cliOptions.outFile || \"\") ||\n        \"stdout\",\n      sections: mapSections,\n    });\n    // For some reason, the spec doesn't allow sourceRoot when constructing a\n    // sectioned sourcemap. But AllMap returns a regular sourcemap, we can\n    // freely add to with a sourceRoot.\n    map.sourceRoot = babelOptions.sourceRoot;\n\n    // add the inline sourcemap comment if we've either explicitly asked for inline source\n    // maps, or we've requested them without any output file\n    if (\n      babelOptions.sourceMaps === \"inline\" ||\n      (!cliOptions.outFile && babelOptions.sourceMaps)\n    ) {\n      code += \"\\n\" + convertSourceMap.fromObject(encodedMap(map)).toComment();\n    }\n\n    return {\n      map: map,\n      code: code,\n      hasRawMap: hasRawMap,\n    };\n  }\n\n  function countNewlines(code: string): number {\n    let count = 0;\n    let index = -1;\n    while ((index = code.indexOf(\"\\n\", index + 1)) !== -1) {\n      count++;\n    }\n    return count;\n  }\n\n  function output(fileResults: Array<FileResult>): void {\n    const result = buildResult(fileResults);\n\n    if (cliOptions.outFile) {\n      fs.mkdirSync(path.dirname(cliOptions.outFile), { recursive: true });\n\n      let outputMap: \"both\" | \"external\" | false = false;\n      if (babelOptions.sourceMaps && babelOptions.sourceMaps !== \"inline\") {\n        outputMap = \"external\";\n      } else if (babelOptions.sourceMaps == null && result.hasRawMap) {\n        outputMap = util.hasDataSourcemap(result.code) ? \"external\" : \"both\";\n      }\n\n      if (outputMap) {\n        const mapLoc = cliOptions.outFile + \".map\";\n        if (outputMap === \"external\") {\n          result.code = util.addSourceMappingUrl(result.code, mapLoc);\n        }\n        fs.writeFileSync(\n          mapLoc,\n          JSON.stringify(encodedMap(result.map as TraceMap)),\n        );\n      }\n\n      fs.writeFileSync(cliOptions.outFile, result.code);\n    } else {\n      process.stdout.write(result.code + \"\\n\");\n    }\n  }\n\n  function readStdin(): Promise<string> {\n    return new Promise((resolve, reject): void => {\n      let code = \"\";\n\n      process.stdin.setEncoding(\"utf8\");\n\n      process.stdin.on(\"readable\", function () {\n        const chunk = process.stdin.read();\n        if (chunk !== null) code += chunk;\n      });\n\n      process.stdin.on(\"end\", function () {\n        resolve(code);\n      });\n      process.stdin.on(\"error\", reject);\n    });\n  }\n\n  async function stdin(): Promise<void> {\n    const code = await readStdin();\n\n    const res = await util.transformRepl(cliOptions.filename, code, {\n      ...babelOptions,\n      sourceFileName: \"stdin\",\n    });\n\n    output([res]);\n  }\n\n  async function walk(filenames: Array<string>): Promise<void> {\n    const _filenames: string[] = [];\n\n    filenames.forEach(function (filename) {\n      if (!fs.existsSync(filename)) return;\n\n      const stat = fs.statSync(filename);\n      if (stat.isDirectory()) {\n        _filenames.push(\n          ...util.readdirForCompilable(\n            filename,\n            cliOptions.includeDotfiles,\n            cliOptions.extensions,\n          ),\n        );\n      } else {\n        _filenames.push(filename);\n      }\n    });\n\n    const results = await Promise.all(\n      _filenames.map(async function (filename: string): Promise<any> {\n        let sourceFilename = filename;\n        if (cliOptions.outFile) {\n          sourceFilename = path.relative(\n            path.dirname(cliOptions.outFile),\n            sourceFilename,\n          );\n        }\n        sourceFilename = slash(sourceFilename);\n\n        try {\n          return await util.compile(filename, {\n            ...babelOptions,\n            sourceFileName: sourceFilename,\n            // Since we're compiling everything to be merged together,\n            // \"inline\" applies to the final output file, but not to the individual\n            // files being concatenated.\n            sourceMaps:\n              babelOptions.sourceMaps === \"inline\"\n                ? true\n                : babelOptions.sourceMaps,\n          });\n        } catch (err) {\n          if (!cliOptions.watch) {\n            throw err;\n          }\n\n          console.error(err);\n          return null;\n        }\n      }),\n    );\n\n    output(results);\n  }\n\n  async function files(filenames: Array<string>): Promise<void> {\n    if (cliOptions.watch) {\n      watcher.enable({ enableGlobbing: false });\n    }\n\n    if (!cliOptions.skipInitialBuild) {\n      await walk(filenames);\n    }\n\n    if (cliOptions.watch) {\n      filenames.forEach(watcher.watch);\n\n      watcher.startWatcher();\n\n      watcher.onFilesChange((changes, event, cause) => {\n        const actionableChange = changes.some(\n          filename =>\n            util.isCompilableExtension(filename, cliOptions.extensions) ||\n            filenames.includes(filename),\n        );\n        if (!actionableChange) return;\n\n        if (cliOptions.verbose) {\n          console.log(`${event} ${cause}`);\n        }\n\n        walk(filenames).catch(err => {\n          console.error(err);\n        });\n      });\n    }\n  }\n\n  if (cliOptions.filenames.length) {\n    await files(cliOptions.filenames);\n  } else {\n    await stdin();\n  }\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,kBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,iBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,cAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,aAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,IAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,GAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAM,IAAA,GAAAL,OAAA;AAEA,IAAAM,OAAA,GAAAN,OAAA;AAAwC,SAAAO,mBAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,cAAAC,CAAA,GAAAP,CAAA,CAAAK,CAAA,EAAAC,CAAA,GAAAE,CAAA,GAAAD,CAAA,CAAAE,KAAA,WAAAT,CAAA,gBAAAE,CAAA,CAAAF,CAAA,KAAAO,CAAA,CAAAG,IAAA,GAAAT,CAAA,CAAAO,CAAA,IAAAG,OAAA,CAAAC,OAAA,CAAAJ,CAAA,EAAAK,IAAA,CAAAV,CAAA,EAAAC,CAAA;AAAA,SAAAU,kBAAAd,CAAA,6BAAAC,CAAA,SAAAC,CAAA,GAAAa,SAAA,aAAAJ,OAAA,WAAAR,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAL,CAAA,CAAAgB,KAAA,CAAAf,CAAA,EAAAC,CAAA,YAAAe,MAAAjB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,UAAAlB,CAAA,cAAAkB,OAAAlB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,WAAAlB,CAAA,KAAAiB,KAAA;AAAA,SAAAE,SAAAC,EAAA;EAAA,OAAAC,IAAA,CAAAL,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAAM,KAAA;EAAAA,IAAA,GAAAP,iBAAA,CAgBzB,WAAgB;IAC7BQ,UAAU;IACVC;EACU,CAAC,EAAiB;IAC5B,SAASC,WAAWA,CAACC,WAA8B,EAAqB;MACtE,MAAMC,WAA2C,GAAG,EAAE;MAEtD,IAAIC,IAAI,GAAG,EAAE;MACb,IAAIC,MAAM,GAAG,CAAC;MAEd,IAAIC,SAAS,GAAG,KAAK;MAErB,KAAK,MAAMC,MAAM,IAAIL,WAAW,EAAE;QAChC,IAAI,CAACK,MAAM,EAAE;QAEbD,SAAS,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG;QAExBL,WAAW,CAACM,IAAI,CAAC;UACfJ,MAAM,EAAE;YAAEK,IAAI,EAAEL,MAAM;YAAEM,MAAM,EAAE;UAAE,CAAC;UACnCH,GAAG,EAAGD,MAAM,CAACC,GAAG,IAAyB;YACvCI,OAAO,EAAE,CAAC;YACVC,KAAK,EAAE,EAAE;YACTC,OAAO,EAAE,EAAE;YACXC,QAAQ,EAAE;UACZ;QACF,CAAC,CAAC;QAEFX,IAAI,IAAIG,MAAM,CAACH,IAAI,GAAG,IAAI;QAC1BC,MAAM,IAAIW,aAAa,CAACT,MAAM,CAACH,IAAI,CAAC,GAAG,CAAC;MAC1C;MAEA,MAAMI,GAAG,GAAG,KAAIS,sBAAM,EAAC;QACrBL,OAAO,EAAE,CAAC;QACVM,IAAI,EACFnB,UAAU,CAACoB,eAAe,IAC1BC,MAAGA,CAAC,CAACC,QAAQ,CAACtB,UAAU,CAACuB,OAAO,IAAI,EAAE,CAAC,IACvC,QAAQ;QACVC,QAAQ,EAAEpB;MACZ,CAAC,CAAC;MAIFK,GAAG,CAACgB,UAAU,GAAGxB,YAAY,CAACwB,UAAU;MAIxC,IACExB,YAAY,CAACyB,UAAU,KAAK,QAAQ,IACnC,CAAC1B,UAAU,CAACuB,OAAO,IAAItB,YAAY,CAACyB,UAAW,EAChD;QACArB,IAAI,IAAI,IAAI,GAAGsB,kBAAeA,CAAC,CAACC,UAAU,CAAC,IAAAC,0BAAU,EAACpB,GAAG,CAAC,CAAC,CAACqB,SAAS,CAAC,CAAC;MACzE;MAEA,OAAO;QACLrB,GAAG,EAAEA,GAAG;QACRJ,IAAI,EAAEA,IAAI;QACVE,SAAS,EAAEA;MACb,CAAC;IACH;IAEA,SAASU,aAAaA,CAACZ,IAAY,EAAU;MAC3C,IAAI0B,KAAK,GAAG,CAAC;MACb,IAAIC,KAAK,GAAG,CAAC,CAAC;MACd,OAAO,CAACA,KAAK,GAAG3B,IAAI,CAAC4B,OAAO,CAAC,IAAI,EAAED,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;QACrDD,KAAK,EAAE;MACT;MACA,OAAOA,KAAK;IACd;IAEA,SAASG,MAAMA,CAAC/B,WAA8B,EAAQ;MACpD,MAAMK,MAAM,GAAGN,WAAW,CAACC,WAAW,CAAC;MAEvC,IAAIH,UAAU,CAACuB,OAAO,EAAE;QACtB,GAAAY,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,aAAAlE,GAAA,GAAAmE,SAAA,GAAAvE,OAAA,aAAAwE,IAAA,EAAarB,MAAGA,CAAC,CAACsB,OAAO,CAAC3C,UAAU,CAACuB,OAAO,CAAC,EAAE;UAAEqB,SAAS,EAAE;QAAK,CAAC,CAAC;QAEnE,IAAIC,SAAsC,GAAG,KAAK;QAClD,IAAI5C,YAAY,CAACyB,UAAU,IAAIzB,YAAY,CAACyB,UAAU,KAAK,QAAQ,EAAE;UACnEmB,SAAS,GAAG,UAAU;QACxB,CAAC,MAAM,IAAI5C,YAAY,CAACyB,UAAU,IAAI,IAAI,IAAIlB,MAAM,CAACD,SAAS,EAAE;UAC9DsC,SAAS,GAAGtE,IAAI,CAACuE,gBAAgB,CAACtC,MAAM,CAACH,IAAI,CAAC,GAAG,UAAU,GAAG,MAAM;QACtE;QAEA,IAAIwC,SAAS,EAAE;UACb,MAAME,MAAM,GAAG/C,UAAU,CAACuB,OAAO,GAAG,MAAM;UAC1C,IAAIsB,SAAS,KAAK,UAAU,EAAE;YAC5BrC,MAAM,CAACH,IAAI,GAAG9B,IAAI,CAACyE,mBAAmB,CAACxC,MAAM,CAACH,IAAI,EAAE0C,MAAM,CAAC;UAC7D;UACAE,IAACA,CAAC,CAACC,aAAa,CACdH,MAAM,EACNI,IAAI,CAACC,SAAS,CAAC,IAAAvB,0BAAU,EAACrB,MAAM,CAACC,GAAe,CAAC,CACnD,CAAC;QACH;QAEAwC,IAACA,CAAC,CAACC,aAAa,CAAClD,UAAU,CAACuB,OAAO,EAAEf,MAAM,CAACH,IAAI,CAAC;MACnD,CAAC,MAAM;QACLiC,OAAO,CAACe,MAAM,CAACC,KAAK,CAAC9C,MAAM,CAACH,IAAI,GAAG,IAAI,CAAC;MAC1C;IACF;IAEA,SAASkD,SAASA,CAAA,EAAoB;MACpC,OAAO,IAAIlE,OAAO,CAAC,CAACC,OAAO,EAAEkE,MAAM,KAAW;QAC5C,IAAInD,IAAI,GAAG,EAAE;QAEbiC,OAAO,CAACmB,KAAK,CAACC,WAAW,CAAC,MAAM,CAAC;QAEjCpB,OAAO,CAACmB,KAAK,CAACE,EAAE,CAAC,UAAU,EAAE,YAAY;UACvC,MAAMC,KAAK,GAAGtB,OAAO,CAACmB,KAAK,CAACI,IAAI,CAAC,CAAC;UAClC,IAAID,KAAK,KAAK,IAAI,EAAEvD,IAAI,IAAIuD,KAAK;QACnC,CAAC,CAAC;QAEFtB,OAAO,CAACmB,KAAK,CAACE,EAAE,CAAC,KAAK,EAAE,YAAY;UAClCrE,OAAO,CAACe,IAAI,CAAC;QACf,CAAC,CAAC;QACFiC,OAAO,CAACmB,KAAK,CAACE,EAAE,CAAC,OAAO,EAAEH,MAAM,CAAC;MACnC,CAAC,CAAC;IACJ;IAAC,SAEcC,KAAKA,CAAA;MAAA,OAAAK,MAAA,CAAApE,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAAqE,OAAA;MAAAA,MAAA,GAAAtE,iBAAA,CAApB,aAAsC;QACpC,MAAMa,IAAI,SAASkD,SAAS,CAAC,CAAC;QAE9B,MAAMQ,GAAG,SAASxF,IAAI,CAACyF,aAAa,CAAChE,UAAU,CAACiE,QAAQ,EAAE5D,IAAI,EAAA6D,MAAA,CAAAC,MAAA,KACzDlE,YAAY;UACfmE,cAAc,EAAE;QAAO,EACxB,CAAC;QAEFlC,MAAM,CAAC,CAAC6B,GAAG,CAAC,CAAC;MACf,CAAC;MAAA,OAAAD,MAAA,CAAApE,KAAA,OAAAD,SAAA;IAAA;IAAA,SAEc4E,IAAIA,CAAAC,GAAA;MAAA,OAAAC,KAAA,CAAA7E,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAA8E,MAAA;MAAAA,KAAA,GAAA/E,iBAAA,CAAnB,WAAoBgF,SAAwB,EAAiB;QAC3D,MAAMC,UAAoB,GAAG,EAAE;QAE/BD,SAAS,CAACE,OAAO,CAAC,UAAUT,QAAQ,EAAE;UACpC,IAAI,CAAChB,IAACA,CAAC,CAAC0B,UAAU,CAACV,QAAQ,CAAC,EAAE;UAE9B,MAAMW,IAAI,GAAG3B,IAACA,CAAC,CAAC4B,QAAQ,CAACZ,QAAQ,CAAC;UAClC,IAAIW,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;YACtBL,UAAU,CAAC/D,IAAI,CACb,GAAGnC,IAAI,CAACwG,oBAAoB,CAC1Bd,QAAQ,EACRjE,UAAU,CAACgF,eAAe,EAC1BhF,UAAU,CAACiF,UACb,CACF,CAAC;UACH,CAAC,MAAM;YACLR,UAAU,CAAC/D,IAAI,CAACuD,QAAQ,CAAC;UAC3B;QACF,CAAC,CAAC;QAEF,MAAMiB,OAAO,SAAS7F,OAAO,CAAC8F,GAAG,CAC/BV,UAAU,CAAChE,GAAG,CAAAjB,iBAAA,CAAC,WAAgByE,QAAgB,EAAgB;UAC7D,IAAImB,cAAc,GAAGnB,QAAQ;UAC7B,IAAIjE,UAAU,CAACuB,OAAO,EAAE;YACtB6D,cAAc,GAAG/D,MAAGA,CAAC,CAACgE,QAAQ,CAC5BhE,MAAGA,CAAC,CAACsB,OAAO,CAAC3C,UAAU,CAACuB,OAAO,CAAC,EAChC6D,cACF,CAAC;UACH;UACAA,cAAc,GAAGE,OAAIA,CAAC,CAACF,cAAc,CAAC;UAEtC,IAAI;YACF,aAAa7G,IAAI,CAACgH,OAAO,CAACtB,QAAQ,EAAAC,MAAA,CAAAC,MAAA,KAC7BlE,YAAY;cACfmE,cAAc,EAAEgB,cAAc;cAI9B1D,UAAU,EACRzB,YAAY,CAACyB,UAAU,KAAK,QAAQ,GAChC,IAAI,GACJzB,YAAY,CAACyB;YAAU,EAC9B,CAAC;UACJ,CAAC,CAAC,OAAO8D,GAAG,EAAE;YACZ,IAAI,CAACxF,UAAU,CAACyF,KAAK,EAAE;cACrB,MAAMD,GAAG;YACX;YAEAE,OAAO,CAACC,KAAK,CAACH,GAAG,CAAC;YAClB,OAAO,IAAI;UACb;QACF,CAAC,EACH,CAAC;QAEDtD,MAAM,CAACgD,OAAO,CAAC;MACjB,CAAC;MAAA,OAAAX,KAAA,CAAA7E,KAAA,OAAAD,SAAA;IAAA;IAAA,SAEcmG,KAAKA,CAAAC,GAAA;MAAA,OAAAC,MAAA,CAAApG,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAAqG,OAAA;MAAAA,MAAA,GAAAtG,iBAAA,CAApB,WAAqBgF,SAAwB,EAAiB;QAC5D,IAAIxE,UAAU,CAACyF,KAAK,EAAE;UACpBjH,OAAO,CAACuH,MAAM,CAAC;YAAEC,cAAc,EAAE;UAAM,CAAC,CAAC;QAC3C;QAEA,IAAI,CAAChG,UAAU,CAACiG,gBAAgB,EAAE;UAChC,MAAM5B,IAAI,CAACG,SAAS,CAAC;QACvB;QAEA,IAAIxE,UAAU,CAACyF,KAAK,EAAE;UACpBjB,SAAS,CAACE,OAAO,CAAClG,OAAO,CAACiH,KAAK,CAAC;UAEhCjH,OAAO,CAAC0H,YAAY,CAAC,CAAC;UAEtB1H,OAAO,CAAC2H,aAAa,CAAC,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,KAAK;YAC/C,MAAMC,gBAAgB,GAAGH,OAAO,CAACI,IAAI,CACnCvC,QAAQ,IACN1F,IAAI,CAACkI,qBAAqB,CAACxC,QAAQ,EAAEjE,UAAU,CAACiF,UAAU,CAAC,IAC3DT,SAAS,CAACkC,QAAQ,CAACzC,QAAQ,CAC/B,CAAC;YACD,IAAI,CAACsC,gBAAgB,EAAE;YAEvB,IAAIvG,UAAU,CAAC2G,OAAO,EAAE;cACtBjB,OAAO,CAACkB,GAAG,CAAC,GAAGP,KAAK,IAAIC,KAAK,EAAE,CAAC;YAClC;YAEAjC,IAAI,CAACG,SAAS,CAAC,CAACqC,KAAK,CAACrB,GAAG,IAAI;cAC3BE,OAAO,CAACC,KAAK,CAACH,GAAG,CAAC;YACpB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF,CAAC;MAAA,OAAAM,MAAA,CAAApG,KAAA,OAAAD,SAAA;IAAA;IAED,IAAIO,UAAU,CAACwE,SAAS,CAACsC,MAAM,EAAE;MAC/B,MAAMlB,KAAK,CAAC5F,UAAU,CAACwE,SAAS,CAAC;IACnC,CAAC,MAAM;MACL,MAAMf,KAAK,CAAC,CAAC;IACf;EACF,CAAC;EAAA,OAAA1D,IAAA,CAAAL,KAAA,OAAAD,SAAA;AAAA", "ignoreList": []}