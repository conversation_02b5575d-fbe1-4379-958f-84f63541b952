#!/usr/bin/env node
'use strict';

var minimumVersion = '12.10';
var recommendedVersion = '14.5';
var currentVersion = process.versions.node;

function isNodeLT(v) {
  var check = v.split('.').map(Number);
  var node = currentVersion.split('.').map(Number);
  return node[0] < check[0] || (node[0] === check[0] && node[1] < check[1]);
}

if (isNodeLT(minimumVersion)) {
  console.error(
    '\nYour current version of Node is v' +
      currentVersion +
      ', however Stencil requires v' +
      minimumVersion +
      '.0 or greater. It is recommended to use an Active LTS version of Node (https://nodejs.org/en/about/releases/).\n',
  );
  process.exit(1);
}

if (isNodeLT(recommendedVersion)) {
  console.warn(
    '\nYour current version of Node is v' +
      currentVersion +
      ", however Stencil's recommendation is v" +
      recommendedVersion +
      '.0 or greater. Note that future versions of Stencil will eventually remove support for non-LTS Node versions and an Active LTS version is recommended (https://nodejs.org/en/about/releases/).\n',
  );
}

var cli = require('../cli/index.cjs');
var nodeApi = require('../sys/node/index.js');
var nodeLogger = nodeApi.createNodeLogger({ process: process });
var nodeSys = nodeApi.createNodeSys({ process: process, logger: nodeLogger });

nodeApi.setupNodeProcess({ process: process, logger: nodeLogger });

cli.run({
  args: process.argv.slice(2),
  logger: nodeLogger,
  sys: nodeSys,
  checkVersion: nodeApi.checkVersion
})
.catch(function (err) {
  console.error('uncaught error', err);
  process.exit(1);
});
