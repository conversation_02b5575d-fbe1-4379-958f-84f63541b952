<!doctype html><html><head><meta charset="utf-8"><title>Stencil Dev Server Connector 2.10.0 &#9889</title><style>body{background:black;color:white;font:18px monospace;text-align:center}</style></head><body>

Stencil Dev Server Connector 2.10.0 &#9889;

<script>!function(e,t,r,n){"use strict";var o=function(e){a(),i(e)},i=function(e){function t(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var r=document.createEvent("CustomEvent");return r.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),r}"function"!=typeof e.CustomEvent&&(t.prototype=e.Event.prototype,e.CustomEvent=t)},a=function(){"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e){var t,r,n,o=Object(e);for(t=1;t<arguments.length;t++)if(null!=(r=arguments[t]))for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(o[n]=r[n]);return o},writable:!0,configurable:!0})},s="/~dev-server",l=s+"-init",d=s+"-open-in-editor",c="#dev-server-modal * { box-sizing: border-box !important; } #dev-server-modal { direction: ltr !important; display: block !important; position: absolute !important; top: 0 !important; right: 0 !important; bottom: 0 !important; left: 0 !important; z-index: 100000; margin: 0 !important; padding: 0 !important; font-family: -apple-system, 'Roboto', BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important; font-size: 14px !important; line-height: 1.5 !important; -webkit-font-smoothing: antialiased; text-rendering: optimizeLegibility; text-size-adjust: none; word-wrap: break-word; color: #333 !important; background-color: white !important; box-sizing: border-box !important; overflow: hidden; user-select: auto; } #dev-server-modal-inner { position: relative !important; padding: 0 0 30px 0 !important; width: 100% !important; height: 100%; overflow-x: hidden; overflow-y: scroll; -webkit-overflow-scrolling: touch; } .dev-server-diagnostic { margin: 20px !important; border: 1px solid #ddd !important; border-radius: 3px !important; } .dev-server-diagnostic-masthead { padding: 8px 12px 12px 12px !important; } .dev-server-diagnostic-title { margin: 0 !important; font-weight: bold !important; color: #222 !important; } .dev-server-diagnostic-message { margin-top: 4px !important; color: #555 !important; } .dev-server-diagnostic-file { position: relative !important; border-top: 1px solid #ddd !important; } .dev-server-diagnostic-file-header { display: block !important; padding: 5px 10px !important; color: #555 !important; border-bottom: 1px solid #ddd !important; border-top-left-radius: 2px !important; border-top-right-radius: 2px !important; background-color: #f9f9f9 !important; font-family: Consolas, 'Liberation Mono', Menlo, Courier, monospace !important; font-size: 12px !important; } a.dev-server-diagnostic-file-header { color: #0000ee !important; text-decoration: underline !important; } a.dev-server-diagnostic-file-header:hover { text-decoration: none !important; background-color: #f4f4f4 !important; } .dev-server-diagnostic-file-name { font-weight: bold !important; } .dev-server-diagnostic-blob { overflow-x: auto !important; overflow-y: hidden !important; border-bottom-right-radius: 3px !important; border-bottom-left-radius: 3px !important; } .dev-server-diagnostic-table { margin: 0 !important; padding: 0 !important; border-spacing: 0 !important; border-collapse: collapse !important; border-width: 0 !important; border-style: none !important; -moz-tab-size: 2; tab-size: 2; } .dev-server-diagnostic-table td, .dev-server-diagnostic-table th { padding: 0 !important; border-width: 0 !important; border-style: none !important; } td.dev-server-diagnostic-blob-num { padding-right: 10px !important; padding-left: 10px !important; width: 1% !important; min-width: 50px !important; font-family: Consolas, 'Liberation Mono', Menlo, Courier, monospace !important; font-size: 12px !important; line-height: 20px !important; color: rgba(0, 0, 0, 0.3) !important; text-align: right !important; white-space: nowrap !important; vertical-align: top !important; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: solid #eee !important; border-width: 0 1px 0 0 !important; } td.dev-server-diagnostic-blob-num::before { content: attr(data-line-number) !important; } .dev-server-diagnostic-error-line td.dev-server-diagnostic-blob-num { background-color: #ffdddd !important; border-color: #ffc9c9 !important; } .dev-server-diagnostic-error-line td.dev-server-diagnostic-blob-code { background: rgba(255, 221, 221, 0.25) !important; z-index: -1; } .dev-server-diagnostic-open-in-editor td.dev-server-diagnostic-blob-num:hover { cursor: pointer; background-color: #ffffe3 !important; font-weight: bold; } .dev-server-diagnostic-open-in-editor.dev-server-diagnostic-error-line td.dev-server-diagnostic-blob-num:hover { background-color: #ffdada !important; } td.dev-server-diagnostic-blob-code { position: relative !important; padding-right: 10px !important; padding-left: 10px !important; line-height: 20px !important; vertical-align: top !important; overflow: visible !important; font-family: Consolas, 'Liberation Mono', Menlo, Courier, monospace !important; font-size: 12px !important; color: #333 !important; word-wrap: normal !important; white-space: pre !important; } td.dev-server-diagnostic-blob-code::before { content: '' !important; } .dev-server-diagnostic-error-chr { position: relative !important; } .dev-server-diagnostic-error-chr::before { position: absolute !important; z-index: -1; top: -3px !important; left: 0px !important; width: 8px !important; height: 20px !important; background-color: #ffdddd !important; content: '' !important; } /** * GitHub Gist Theme * Author : Louis Barranqueiro - https://github.com/LouisBarranqueiro * https://highlightjs.org/ */ .hljs-comment, .hljs-meta { color: #969896; } .hljs-string, .hljs-variable, .hljs-template-variable, .hljs-strong, .hljs-emphasis, .hljs-quote { color: #df5000; } .hljs-keyword, .hljs-selector-tag, .hljs-type { color: #a71d5d; } .hljs-literal, .hljs-symbol, .hljs-bullet, .hljs-attribute { color: #0086b3; } .hljs-section, .hljs-name { color: #63a35c; } .hljs-tag { color: #333333; } .hljs-title, .hljs-attr, .hljs-selector-id, .hljs-selector-class, .hljs-selector-attr, .hljs-selector-pseudo { color: #795da3; } .hljs-addition { color: #55a532; background-color: #eaffea; } .hljs-deletion { color: #bd2c00; background-color: #ffecec; } .hljs-link { text-decoration: underline; }",A=function(e){var t,r,n={diagnostics:[],status:null};return e&&e.window&&Array.isArray(e.buildResults.diagnostics)&&(t=e.buildResults.diagnostics.filter((function(e){return"error"===e.level}))).length>0&&(r=m(e.window.document),t.forEach((function(t){n.diagnostics.push(t),p(e.window.document,e.openInEditor,r,t)})),n.status="error"),n},p=function(e,t,r,n){var o,i,a,s,l,d,c,A,p,m,f,y,w=e.createElement("div");w.className="dev-server-diagnostic",(o=e.createElement("div")).className="dev-server-diagnostic-masthead",o.title=g(n.type)+" error: "+g(n.code),w.appendChild(o),(i=e.createElement("div")).className="dev-server-diagnostic-title","string"==typeof n.header&&n.header.trim().length>0?i.textContent=n.header:i.textContent=h(n.type)+" "+h(n.level),o.appendChild(i),(a=e.createElement("div")).className="dev-server-diagnostic-message",a.textContent=n.messageText,o.appendChild(a),(s=e.createElement("div")).className="dev-server-diagnostic-file",w.appendChild(s),l="string"==typeof n.absFilePath&&0===n.absFilePath.indexOf("http"),d="function"==typeof t&&"string"==typeof n.absFilePath&&!l,l?((c=e.createElement("a")).href=n.absFilePath,c.setAttribute("target","_blank"),c.setAttribute("rel","noopener noreferrer"),c.className="dev-server-diagnostic-file-header",(A=e.createElement("span")).className="dev-server-diagnostic-file-path",A.textContent=n.absFilePath,c.appendChild(A),s.appendChild(c)):n.relFilePath&&((c=e.createElement(d?"a":"div")).className="dev-server-diagnostic-file-header",n.absFilePath&&(c.title=g(n.absFilePath),d&&u(t,c,n.absFilePath,n.lineNumber,n.columnNumber)),p=n.relFilePath.split("/"),(m=e.createElement("span")).className="dev-server-diagnostic-file-name",m.textContent=p.pop(),(A=e.createElement("span")).className="dev-server-diagnostic-file-path",A.textContent=p.join("/")+"/",c.appendChild(A),c.appendChild(m),s.appendChild(c)),n.lines&&n.lines.length>0&&((f=e.createElement("div")).className="dev-server-diagnostic-blob",s.appendChild(f),(y=e.createElement("table")).className="dev-server-diagnostic-table",f.appendChild(y),b(n.lines).forEach((function(r){var o,i,a,s=e.createElement("tr");r.errorCharStart>0&&s.classList.add("dev-server-diagnostic-error-line"),d&&s.classList.add("dev-server-diagnostic-open-in-editor"),y.appendChild(s),(o=e.createElement("td")).className="dev-server-diagnostic-blob-num",r.lineNumber>0&&(o.setAttribute("data-line-number",r.lineNumber+""),o.title=g(n.relFilePath)+", line "+r.lineNumber,d&&(i=r.lineNumber===n.lineNumber?n.columnNumber:1,u(t,o,n.absFilePath,r.lineNumber,i))),s.appendChild(o),(a=e.createElement("td")).className="dev-server-diagnostic-blob-code",a.innerHTML=v(r.text,r.errorCharStart,r.errorLength),s.appendChild(a)}))),r.appendChild(w)},u=function(e,t,r,n,o){"A"===t.tagName&&(t.href="#open-in-editor"),("number"!=typeof n||n<1)&&(n=1),("number"!=typeof o||o<1)&&(o=1),t.addEventListener("click",(function(t){t.preventDefault(),t.stopPropagation(),e({file:r,line:n,column:o})}))},m=function(e){var t=e.getElementById(w);return t||((t=e.createElement("div")).id=w,t.setAttribute("role","dialog"),e.body.appendChild(t)),t.innerHTML="<style>"+c+'</style><div id="'+w+'-inner"></div>',e.getElementById(w+"-inner")},f=function(e){var t=e.window.document.getElementById(w);t&&t.parentNode.removeChild(t)},g=function(e){return"number"==typeof e||"boolean"==typeof e?e.toString():"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"):""},h=function(e){return e.charAt(0).toUpperCase()+e.substr(1)},v=function(e,t,r){if("string"!=typeof e)return"";var n=t+r;return e.split("").map((function(e,r){var o;return o="<"===e?"&lt;":">"===e?"&gt;":'"'===e?"&quot;":"'"===e?"&#039;":"&"===e?"&amp;":e,r>=t&&r<n&&(o='<span class="dev-server-diagnostic-error-chr">'+o+"</span>"),o})).join("")},b=function(e){var t,r,n=JSON.parse(JSON.stringify(e));for(t=0;t<100;t++){if(!y(n))return n;for(r=0;r<n.length;r++)if(n[r].text=n[r].text.substr(1),n[r].errorCharStart--,!n[r].text.length)return n}return n},y=function(e){var t,r;if(!e.length)return!1;for(t=0;t<e.length;t++){if(!e[t].text||e[t].text.length<1)return!1;if(" "!==(r=e[t].text.charAt(0))&&"\t"!==r)return!1}return!0},w="dev-server-modal",k=function(e,t){e.dispatchEvent(new CustomEvent(I,{detail:t}))},E=function(e,t){e.dispatchEvent(new CustomEvent(Q,{detail:t}))},C=function(e,t){e.dispatchEvent(new CustomEvent(H,{detail:t}))},j=function(e,t){e.addEventListener(I,(function(e){t(e.detail)}))},L=function(e,t){e.addEventListener(Q,(function(e){t(e.detail)}))},x=function(e,t){e.addEventListener(H,(function(e){t(e.detail)}))},I="devserver:buildlog",Q="devserver:buildresults",H="devserver:buildstatus",S=function(e){function t(){clearTimeout(s),clearTimeout(a);var e=o();if(!e)return function(){var e=p.createElement("div");e.id=c,e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.zIndex="100001",e.style.width="100%",e.style.height="2px",e.style.transform="scaleX(0)",e.style.opacity="1",e.style.background=u,e.style.transformOrigin="left center",e.style.transition="transform .1s ease-in-out, opacity .5s ease-in",e.style.contain="strict",p.body.appendChild(e)}(),void(i=setTimeout(t,16));e.style.background=u,e.style.opacity="1",e.style.transform="scaleX("+Math.min(1,n())+")",null==l&&(l=setInterval((function(){d+=.05*Math.random()+.01,n()<.9?t():clearInterval(l)}),800))}function r(){clearInterval(l),d=.05,l=null,clearTimeout(s),clearTimeout(i),clearTimeout(a);var e=o();e&&(f>=1&&(e.style.transform="scaleX(1)"),s=setTimeout((function(){try{var e=o();e&&(e.style.opacity="0")}catch(e){}}),150),a=setTimeout((function(){try{var e=o();e&&e.parentNode.removeChild(e)}catch(e){}}),1e3))}function n(){var e=f+d;return Math.max(0,Math.min(1,e))}function o(){return p.getElementById(c)}var i,a,s,l,d,c,A=e.window,p=A.document,u="#5851ff",m="#b70c19",f=0;r(),j(A,(function(e){(f=e.progress)>=0&&f<1?t():r()})),L(A,(function(e){if(e.hasError){var t=o();t&&(t.style.transform="scaleX(1)",t.style.background=m)}r()})),x(A,(function(e){"disabled"===e&&r()})),"tmpl-initial-load"===p.head.dataset.tmpl&&t(),c="dev-server-progress-bar"},B=function(e){var t=e.window,r=t.document,n=D(r);n.forEach((function(e){e.href&&(e.dataset.href=e.href,e.dataset.type=e.type)})),x(t,(function(e){U(r,e)}))},U=function(e,t){D(e).forEach((function(e){N(e,t)}))},N=function(e,t){"pending"===t?(e.href=T,e.type=M,e.setAttribute("data-status",t)):"error"===t?(e.href=R,e.type=M,e.setAttribute("data-status",t)):"disabled"===t?(e.href=O,e.type=M,e.setAttribute("data-status",t)):(e.removeAttribute("data-status"),e.dataset.href?(e.href=e.dataset.href,e.type=e.dataset.type):(e.href=F,e.type=M))},D=function(e){var t,r,n=[],o=e.querySelectorAll("link");for(t=0;t<o.length;t++)o[t].href&&o[t].rel&&(o[t].rel.indexOf("shortcut")>-1||o[t].rel.indexOf("icon")>-1)&&n.push(o[t]);return 0===n.length&&((r=e.createElement("link")).rel="shortcut icon",e.head.appendChild(r),n.push(r)),n},F="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAMAAABlApw1AAAAnFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD4jUzeAAAAM3RSTlMAsGDs4wML8QEbBvr2FMhAM7+ILCUPnNzXrX04otO6j3RiT0ggzLSTcmtWUUWoZlknghZc2mZzAAACrklEQVR42u3dWXLiUAyFYWEwg40x8wxhSIAwJtH+99ZVeeinfriXVpWk5Hyr+C2VrgkAAAAAAAAAAAw5sZQ7aUhYypw07FjKC2ko2yxk2SQFgwYLOWSkYFhlIZ06KWhNWMhqRApGKxYyaZGCeoeFVIekIDuwkEaXFDSXLKRdkoYjS9mRhjlLSUjDO0s5kYYzS+mThn3OQsYqAbQQC7hZSgoGYgHUy0jBa42FvKkEUDERC6CCFIzeWEjtlRRkPbGAG5CCtCIWQAtS0ByzkHxPGvos5UEaNizlnTRsWconhbM4wTpSFHMTrFtKCroNFrLGBOsJLbGAWxWkoFiJBRAmWE/I1r4nWOmNheTeJ1gX0vDJUrYUweAEa04aHs5XePvc9wpPboJ1SCmOsRVkr04aromUEQEAgB9lxaZ++ATFpNDv6Y8qm1QdBk9QTAr9ni6mbFK7DJ6g2LQLXoHZlFCQdMY2nYJXYDb1g1dgNo2boSswm2Zp6ArMptCFyIVtCl2IlDmbNC0QcPEQcD8l4HLvAXdxHnBb5wG3QcDFQ8D9mIDrIeCiIeDiA25oNeA+EHDREHDxAbdmmxBwT0HARQbciW0KDbiEbQoNuB3bFBxwbTYJAfcUBFxkwFG/YlNJAADgxzCRcqUY9m7KGgNSUEx9H3XXO76Puv/OY5wedX/flHk+6j46v2maO79purPvm6Yz+75puua+b5q6Dd/PEsrNMyZfFM5gAMW+ymPtWciYV3ksBpBOwKUH3wHXXLKUM2l4cR5wG+cBlzgPuJ3zgJNb6FRwlP4Ln1X8wrOKeFbxP6Qz3wEn+KzilWLYe5UnMuDwY5BvD+cBt899B9zC+49Bqr4DrlXzHXDF1HfA1Tu+Ay5b+w649OY74OjoO+Bo7jzg7s4DDgAAAAAAAAAA/u0POrfnVIaqz/QAAAAASUVORK5CYII=",T="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAMAAABlApw1AAAAjVBMVEUAAAD8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjL8kjLn7xn3AAAALnRSTlMAsFBgAaDxfPpAdTMcD/fs47kDBhVXJQpvLNbInIiBRvSqIb+TZ2OOONxdzUxpgKSpAAAAA69JREFUeNrt3FtvskAQxvERFQXFioqnCkqth572+3+8947dN00TliF5ZpP53ZOAveg/OzCklFJKKaWUUkoppQTZm77cCGFo+jIhhG/TlwchJAvTk/GIAA6x6Um+JoDti+nJ644A5h+mJ8eMALKj6cnHnAB2r80NLJ4jf3Vz+cuWANZ5cwPTM/l7by6PZwQwGptGQf4q++dLCOHdNIbkb2IvjwjAvYEf8pe6j4/wYxopr/9SQih4BXa3l5eEcJ7a++c9/gkSQE8bcCWvXwcrAjjYADrxHv8KCbi3JasgD5fm8i9IAG1swMXzDv0X2wDaEED21dzA5UDeVoPm8uUbAayvvAI42YA7EIDzA5pv8lc6/UoAoxMv4CZuvyKUpnHn9VNBAG6B7XkBtCeEO6/AbvbyihAiXsB92svfCcA9wap4j19DAmgWs37AZCrnBKvu8vgX9AmWE3BZh/6L7QkWJIA2RxtwHQpml9sAQp9gXWbkbxz4CdYDfIK1qk1j3IV9fPgJFlNECJXhYfSfsBHkhBCKwEd452nYI7wncwQJP8GKTU+uO0I4D/uSkVJKqXAkA5nK9icoIi3nrU9QRHrZtj5BESmetT5BEantPCh7NTJFrUdgMg1bj8BkSv1HYJ8RmjMQKf1HYDdC+/R/IyQFzbD4AxH+CIyPPxCJoEdQ/IFIMgXNEPkDkd8jMLQs5wRcTXA1J+By/BGO+0ovYwQGU3kPRLJfIzCkCSfgpgmhpc5AxD/gIkLb8wKO0DTgoNyaGQQecNfQAy7TgGtHA04DLtyA24UecHngAVdrwIkJuAitU8DJ1Dbghkam9gEnU+uAWxiRjhsdoXagI1TPgKNyIBO+ZpRSSrW3HfblTAA9/juPDwTAfiMK9VG3PY/hwX7Ubc9j+AoCWNWGp+NSH4HflE2IgXUEGPI3TTfmN4ndv2kSsRUJvpUn4W1FShbYb5rc84ySAtzKs3W3IgW4lWfO24q0zsFbebIjaysSjbtt5RHzUf0DHHCrAW8gVYEDzl0LGYW4lefB24uYQgOOfwN7dMANeW/k3DkBJ2CrUNE54GRsFYIHnPNR+iPEgHPWKo5DDDhnrWKeBRhwzlrFeNtlq5CgtYqzAAPODaBzgAH331rFAAOOqsDXKjL3IqboN7ILJ4BCDDh3r3SIAfd0AijEgHP3So/8wQNuvjRBbxVij5A6Bpy8EZJnwIkbIfkFnLwRkm/ASRshXbwDTtYICRRwt7BHqEoppZRSSimllFLqD/8AOXJZHefotiIAAAAASUVORK5CYII=",R="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAMAAABlApw1AAAAkFBMVEUAAAD5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0H5Q0HYvLBZAAAAL3RSTlMAsGDjA/rsC/ElHRUBBssz9pFCvoh0UEcsD9ec3K19OLiiaNLEYlmoVeiCbmE+GuMl4I8AAAKQSURBVHja7d1njupQDIZhAymEUIZQQu9taN7/7q50pfl/TmTJtvQ9q3hzLDsEAAAAAAAAAACGzFjKiTS0WcqONMxZypg0fH5YyLFPChZdFnIYkILil4VcclLw3bCQ85IULM8sZPMlBfmFhfwWpGBwYCHdESnoH1nIz4c0jFnKnDTsWEqbNJxYyow03FjKlDTUKQtZqwTQXizgtgkpWGQsZKIScL0OCxmqBFC5EQugkhQshyyk0yMFgwkLyRakIGmJBdCeFPTXLCStScOUpdwogsEXrBdpuLKUJ4XDC9afKmUh94QUjLy/YGViAZRTOIMBtypJQXn2HUC5WMBleMFqILmzkLSicBZfsB6k4clSrqTh5XyEd3MeQHXqe4Qn94LVSiicwRHkJScNdVvKkgAAwI+qZdM0/AXFpE4v+AXFpKwIfkExKfR7ulyxSWkV/IJi0zx4BGbTm4IkW7ZpFjwCs2kaPAKzad0PHYHZtE1CR2A2TQahIzCbhnnwCMykVYmAi4aAQ8BZ4T3grgi4BhBwCDgbEHCNIOAQcCYg4BpCwCHgLEDAaYgPuDfbhIBrBAGHgDMhNOBo2rKpIgAA8KNoS6kplq2dsu6CFJQr30vd+dD3Uvf/nTLHS93J3flZwrHznaad852mE/veaXqw752mKvW90zTq+j5LWGS+r/J8xQKoU1AUa2chm1zlsXQWUifgkoPvgOsffQccjZ0H3Mx5wL2dB9zcecB9sJTePOBM3cU+46wiziq6C7hk6zvg3J9VfDK7vir0ch5wN+cBV6e+A27v/ccgme+AkxshTXKKYW6EFH0X29gIKTLgzI2QYgPO2ggpLuDsvaDEBZy9EVJcwBkcIT0IAAAAAAAAAADs+AdjeyF69/r87QAAAABJRU5ErkJggg==",O="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAMAAABlApw1AAAAeFBMVEUAAAC4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7+4t7/uGGySAAAAJ3RSTlMAsGAE7OMcAQvxJRX69kHWyL8zq5GIdEcsD5zcfVg4uKLNa1JPZoK/xdPIAAACiklEQVR42u3dW5KqUAyF4QgCCggqIt7t9pb5z/Ccvjz2w95UqpJ0r28Uf2WTQAAAAAAAAAAAYMiWpTxJQ8JSTqThwVI2pKFZsJC3ghTs5izkmpKCcspCljNSkB9ZSLsnBfuWhRxzUjBbspBpSQrSKwuZr0lB8cZCFg1p2LCUB2k4sZSENNxYypY0nFlKTxqGmoUcClJwEQu4SUoKdmIBtEpJQZ6xkHeVAKqOYgFUkYL9OwvJclKQrsQCbkcK0olYAF1IQXFgIfVAGnqWcqZwFidYN4phb4L1onCYYMlPsLqUFKwxwRozwTIYcG1FCqrWdwBhgqU7wUo7FlJ7n2DdScPL+RPezfkT3tl5AA217yc89xMssYBbzUjDkEjZEwAA+NFMbOrDJygmZXnwBMWkaRk8QTFpvg6eoJi0aIInKDY9gp/AbEqCJyg2bYOfwGzqKUzPNh2K0Ccwm0IfRBK2KfSLkDvbFPog0tRsUlsh4EZAwP2SgKu9B9wdATcOAg4BZwACbgQEHALOCATcCAg4BJwVCLhREHB/LOAebFNwwC3YJATcKAi4yICjfmJTQwAA4EeZSBkojrWdsvmO4hjbKYtd6ra2Uxa71G1tp0xnqbvo+IPfpe4Nf3K703Ridr3T9OQPfnea7szseaepqX3vNH3NM/xe5fmeZ7i9yiMXQFlJEeydhYy4ymMygCICzmQAxQactbOQMQFnMoBiAs7iVaHIgDN3VSgq4AxeFYoOOGNXhbCUPkaJs4o4q/iXzyp2vgPO/VnFl/OAu/F/jq8KnZ0H3FD7DriL9x+DTH0HXJ75Driq9R1ws6XvgEuvvgOu6HwHHG18BxydnAfc03nAAQAAAAAAAADAz/4BoL2Us9XM2zMAAAAASUVORK5CYII=",M="image/x-icon",z=function(e){return W(G,"Build",e)},K=function(e){return P("Reload",e)},P=function(e,t){return W(X,e,t)},J=function(e,t){return W(Z,e,t)},Y=function(e){var t,r=e,n=q,o="Error";"warn"===r.level&&(n=X,o="Warning"),r.header&&(o=r.header),t="",r.relFilePath&&(t+=r.relFilePath,"number"==typeof r.lineNumber&&r.lineNumber>0&&(t+=", line "+r.lineNumber,"number"==typeof r.columnNumber&&r.columnNumber>0&&(t+=", column "+r.columnNumber)),t+="\n"),t+=r.messageText,W(n,o,t)},W=function(e,t,r){"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.indexOf("Trident")>-1?console.log(t,r):console.log.apply(console,["%c"+t,"background: "+e+"; color: white; padding: 2px 3px; border-radius: 2px; font-size: 0.8em;",r])},X="#f39c12",q="#c0392b",G="#3498db",Z="#717171",V=function(e,t,r){return"string"==typeof r&&""!==r.trim()&&_(t)===_(r)?re(r,e):r},_=function(e){var t=e.split("/");return t[t.length-1].split("&")[0].split("?")[0]},$=function(e){var t={};return"string"==typeof e&&e.split("&").forEach((function(e){var r=e.split("=");t[r[0]]=r[1]?r[1]:""})),t},ee=function(e){return Object.keys(e).map((function(t){return t+"="+e[t]})).join("&")},te=function(e,t,r){var n=e.split("?"),o=n[0],i=$(n[1]);return i[t]=r,o+"?"+ee(i)},re=function(e,t){return te(e,"s-hmr",t)},ne=function(e,t,r){for(var n,o,i=/url\((['"]?)(.*)\1\)/gi,a=r;null!==(n=i.exec(r));)o=n[2],a=a.replace(o,V(e,t,o));return a},oe=function(e){return"link"===e.nodeName.toLowerCase()&&e.href&&e.rel&&"stylesheet"===e.rel.toLowerCase()},ie=function(e){return"template"===e.nodeName.toLowerCase()&&!!e.content&&11===e.content.nodeType},ae=function(e,t){return e.setAttribute("data-hmr",t)},se=function(e){return!!e.shadowRoot&&11===e.shadowRoot.nodeType&&e.shadowRoot!==e},le=function(e){return!!e&&1===e.nodeType&&!!e.getAttribute},de=function(e,t,r){var n=[];return r.forEach((function(r){ce(n,e,t,r)})),n.sort()},ce=function(e,t,r,n){if(t.nodeName.toLowerCase()===n&&"function"==typeof t["s-hmr"]&&(t["s-hmr"](r),ae(t,r),-1===e.indexOf(n)&&e.push(n)),se(t)&&ce(e,t.shadowRoot,r,n),t.children)for(var o=0;o<t.children.length;o++)ce(e,t.children[o],r,n)},Ae=function(e,t,r){if(oe(e)&&r.forEach((function(r){pe(e,t,r)})),ie(e)&&Ae(e.content,t,r),se(e)&&Ae(e.shadowRoot,t,r),e.children)for(var n=0;n<e.children.length;n++)Ae(e.children[n],t,r);return r.sort()},pe=function(e,t,r){var n=e.getAttribute("href"),o=V(t,r,e.href);o!==n&&(e.setAttribute("href",o),ae(e,t))},ue=function(e,t,r,n){return"file:"!==e.location.protocol&&t.styleSheets&&me(t,r,n),he(e,t.documentElement,r,n),n.sort()},me=function(e,t,r){var n,o=Object.keys(e.documentElement.style).filter((function(e){return e.endsWith("Image")}));for(n=0;n<e.styleSheets.length;n++)fe(o,e.styleSheets[n],t,r)},fe=function(e,t,r,n){var o,i,a;try{for(o=t.cssRules,i=0;i<o.length;i++)switch((a=o[i]).type){case CSSRule.IMPORT_RULE:fe(e,a.styleSheet,r,n);break;case CSSRule.STYLE_RULE:ge(e,a,r,n);break;case CSSRule.MEDIA_RULE:fe(e,a,r,n)}}catch(e){console.error("hmrStyleSheetImages: "+e)}},ge=function(e,t,r,n){e.forEach((function(e){n.forEach((function(n){var o=t.style[e],i=ne(r,n,o);o!==i&&(t.style[e]=i)}))}))},he=function(e,t,r,n){var o,i,a=t.nodeName.toLowerCase();if("img"===a&&ve(t,r,n),le(t)&&(o=t.getAttribute("style"))&&be(t,r,n,o),"style"===a&&ye(t,r,n),"file:"!==e.location.protocol&&oe(t)&&we(t,r,n),ie(t)&&he(e,t.content,r,n),se(t)&&he(e,t.shadowRoot,r,n),t.children)for(i=0;i<t.children.length;i++)he(e,t.children[i],r,n)},ve=function(e,t,r){r.forEach((function(r){var n=e.getAttribute("src"),o=V(t,r,n);o!==n&&(e.setAttribute("src",o),ae(e,t))}))},be=function(e,t,r,n){r.forEach((function(r){var o=ne(t,r,n);o!==n&&(e.setAttribute("style",o),ae(e,t))}))},ye=function(e,t,r){r.forEach((function(r){var n=e.innerHTML,o=ne(t,r,n);o!==n&&(e.innerHTML=o,ae(e,t))}))},we=function(e,t,r){e.href=te(e.href,"s-hmr-urls",r.sort().join(",")),e.href=re(e.href,t),e.setAttribute("data-hmr",t)},ke=function(e,t,r){var n,o=r;if(le(e)&&"style"===e.nodeName.toLowerCase()&&o.forEach((function(r){Ee(e,t,r)})),ie(e)&&ke(e.content,t,o),se(e)&&ke(e.shadowRoot,t,o),e.children)for(n=0;n<e.children.length;n++)ke(e.children[n],t,o);return o.map((function(e){return e.styleTag})).reduce((function(e,t){return-1===e.indexOf(t)&&e.push(t),e}),[]).sort()},Ee=function(e,t,r){e.getAttribute("sty-id")===r.styleId&&r.styleText&&(e.innerHTML=r.styleText.replace(/\\n/g,"\n"),e.setAttribute("data-hmr",t))},Ce=function(e){var t,r,n,o,i,a={updatedComponents:[],updatedExternalStyles:[],updatedInlineStyles:[],updatedImages:[],versionId:""};try{if(!(e&&e.window&&e.window.document.documentElement&&e.hmr&&"string"==typeof e.hmr.versionId))return a;r=(t=e.window).document,n=e.hmr,o=r.documentElement,i=n.versionId,a.versionId=i,n.componentsUpdated&&(a.updatedComponents=de(o,i,n.componentsUpdated)),n.inlineStylesUpdated&&(a.updatedInlineStyles=ke(o,i,n.inlineStylesUpdated)),n.externalStylesUpdated&&(a.updatedExternalStyles=Ae(o,i,n.externalStylesUpdated)),n.imagesUpdated&&(a.updatedImages=ue(t,r,i,n.imagesUpdated)),ae(o,i)}catch(e){console.error(e)}return a},je=function(e,t){L(e,(function(r){Le(e,t,r)}))},Le=function(e,t,r){var n,o;try{if(r.buildId===e["s-build-id"])return;if(e["s-build-id"]=r.buildId,f({window:e}),r.hasError)return n=Array.isArray(t.editors)&&t.editors.length>0?t.editors[0].id:null,(o=A({window:e,buildResults:r,openInEditor:n?function(t){var r={file:t.file,line:t.line,column:t.column,editor:n},o=d+"?"+Object.keys(r).map((function(e){return e+"="+r[e]})).join("&");e.fetch(o)}:null})).diagnostics.forEach(Y),void C(e,o.status);if(e["s-initial-load"])return void Ie(e,t,(function(){K("Initial load"),e.location.reload(!0)}));r.hmr&&xe(e,r.hmr)}catch(e){console.error(e)}},xe=function(e,t){var r,n=!1;"pageReload"===t.reloadStrategy&&(n=!0),t.indexHtmlUpdated&&(K("Updated index.html"),n=!0),t.serviceWorkerUpdated&&(K("Updated Service Worker: sw.js"),n=!0),t.scriptsAdded&&t.scriptsAdded.length>0&&(K("Added scripts: "+t.scriptsAdded.join(", ")),n=!0),t.scriptsDeleted&&t.scriptsDeleted.length>0&&(K("Deleted scripts: "+t.scriptsDeleted.join(", ")),n=!0),t.excludeHmr&&t.excludeHmr.length>0&&(K("Excluded From Hmr: "+t.excludeHmr.join(", ")),n=!0),n?e.location.reload(!0):((r=Ce({window:e,hmr:t})).updatedComponents.length>0&&z("Updated component"+(r.updatedComponents.length>1?"s":"")+": "+r.updatedComponents.join(", ")),r.updatedInlineStyles.length>0&&z("Updated styles: "+r.updatedInlineStyles.join(", ")),r.updatedExternalStyles.length>0&&z("Updated stylesheets: "+r.updatedExternalStyles.join(", ")),r.updatedImages.length>0&&z("Updated images: "+r.updatedImages.join(", ")))},Ie=function(e,t,r){e.history.replaceState({},"App",t.basePath),e.navigator.serviceWorker&&e.navigator.serviceWorker.getRegistration?e.navigator.serviceWorker.getRegistration().then((function(e){e?e.unregister().then((function(e){e&&z("unregistered service worker"),r()})):r()})).catch((function(e){P("Service Worker",e),r()})):r()},Qe=function(e,t){function r(){var t=this;A>0&&C(e,"pending"),p||(c=setInterval((function(){if(u++,!p&&t.readyState===WebSocket.OPEN&&u<500){t.send(JSON.stringify({requestBuildResults:!0}))}else clearInterval(c)}),Ue)),clearTimeout(d)}function n(){s()}function o(t){C(e,"disabled"),t.code>Be?P("Dev Server","web socket closed: "+t.code+" "+t.reason):J("Dev Server","Disconnected, attempting to reconnect..."),s()}function i(t){var r=JSON.parse(t.data);if(A>0){if(r.isActivelyBuilding)return;if(r.buildResults)return K("Reconnected to dev server"),p=!0,u=0,clearInterval(c),e["s-build-id"]!==r.buildResults.buildId&&e.location.reload(!0),void(e["s-build-id"]=r.buildResults.buildId)}return r.buildLog?(r.buildLog.progress<1&&C(e,"pending"),void k(e,r.buildLog)):r.buildResults?(p=!0,u=0,clearInterval(c),C(e,"default"),void E(e,r.buildResults)):void 0}function a(){clearTimeout(d),(l=new e.WebSocket(t.socketUrl,["xmpp"])).addEventListener("open",r),l.addEventListener("error",n),l.addEventListener("close",o),l.addEventListener("message",i)}function s(){p=!1,l&&(l.readyState!==WebSocket.OPEN&&l.readyState!==WebSocket.CONNECTING||l.close(Be),l.removeEventListener("open",r),l.removeEventListener("error",n),l.removeEventListener("close",o),l.removeEventListener("message",i),l=null),clearTimeout(d),A>=He?P("Dev Server","Canceling reconnect attempts"):(A++,d=setTimeout(a,Se),C(e,"disabled"))}var l,d,c,A=0,p=!1,u=0;a()},He=1e3,Se=2500,Be=1e3,Ue=500,Ne=function(e,t){try{if(e["s-dev-server"])return;e["s-dev-server"]=!0,B({window:e}),S({window:e}),je(e,t),De(e,t)?(e["s-initial-load"]=!0,Ie(e,t,(function(){Qe(e,t)}))):Qe(e,t)}catch(e){console.error(e)}},De=function(e,t){var r=e.location.pathname;return(r="/"+r.substring(t.basePath.length))===l},Fe={basePath:t.location.pathname,editors:[],reloadStrategy:"hmr",socketUrl:("https:"===location.protocol?"wss:":"ws:")+"//"+location.hostname+(""!==location.port?":"+location.port:"")+"/"};o(e),Ne(t,Object.assign({},Fe,t.devServerConfig,r))}(window,window.parent,window.__DEV_CLIENT_CONFIG__);
</script></body></html>