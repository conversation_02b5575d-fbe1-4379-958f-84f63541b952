import{r as e,h as t}from"./p-fbbae598.js";class s{constructor(t){e(this,t),this.appSrcUrl="",this.imagesUrl="/data/images/",this.buildsUrl="/data/builds/"}async componentWillLoad(){let e="master";this.match&&this.match.params.buildId&&(e=this.match.params.buildId.substr(0,7));let t=`${this.buildsUrl}${e}.json`;"master"===e&&(t+="?ts="+Date.now());const s=await fetch(t);s.ok&&(this.build=await s.json(),document.title=`${this.build.id} Preview: ${this.build.message}`)}render(){const e=[];return this.build&&this.build.screenshots.forEach(t=>{const s=t.testPath.split("/");s.pop();const i=`/data/tests/${this.build.id}/${s.join("/")}/`;if(!e.some(e=>e.url===i)){const s={desc:t.desc.split(",")[0],url:i};e.push(s)}}),e.sort((e,t)=>e.desc.toLowerCase()<t.desc.toLowerCase()?-1:e.desc.toLowerCase()>t.desc.toLowerCase()?1:0),[t("compare-header",{appSrcUrl:this.appSrcUrl}),t("section",{class:"scroll-y"},t("section",{class:"content"},this.build?t("h1",null,t("a",{href:this.build.url},this.build.message)):null,e.map(e=>t("div",null,t("a",{href:e.url},e.desc)))))]}}s.style="screenshot-preview{display:block}screenshot-preview .scroll-y{width:100%}screenshot-preview h1{color:var(--analysis-data-color);font-size:16px;margin:0}screenshot-preview .content{padding:80px 20px 140px 20px}screenshot-preview a{display:block;padding:8px;color:var(--analysis-data-color);text-decoration:none}screenshot-preview a:hover{text-decoration:underline}screenshot-preview compare-header{left:0;padding:0;width:100%;width:100%;height:auto;padding-top:env(safe-area-inset-top)}screenshot-preview compare-header compare-filter{display:none}@media (max-width: 480px){screenshot-preview a{padding:12px;font-size:18px}screenshot-preview a:hover{text-decoration:none}}";export{s as screenshot_preview}