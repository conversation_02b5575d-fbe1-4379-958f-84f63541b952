{"version": 3, "names": ["_path", "data", "require", "fileToDeps", "Map", "depToFiles", "isWatchMode", "watcher", "watchQueue", "Set", "hasStarted", "enable", "enableGlobbing", "FSWatcher", "requireChokidar", "options", "disableGlobbing", "persistent", "ignoreInitial", "awaitWriteFinish", "stabilityThreshold", "pollInterval", "on", "unwatchFile", "startWatcher", "dep", "add", "clear", "console", "log", "watch", "filename", "Error", "path", "resolve", "onFilesChange", "callback", "event", "_depToFiles$get", "absoluteFile", "get", "updateExternalDependencies", "dependencies", "absFilename", "absDependencies", "Array", "from", "deps", "has", "removeFileDependency", "set", "delete", "size", "unwatch", "parseInt", "process", "versions", "node", "err", "error"], "sources": ["../../src/babel/watcher.ts"], "sourcesContent": ["import { createRequire } from \"node:module\";\nimport path from \"node:path\";\nimport type { WatchOptions, FSWatcher } from \"chokidar\";\n\nconst fileToDeps = new Map<string, Set<string>>();\nconst depToFiles = new Map<string, Set<string>>();\n\nlet isWatchMode = false;\nlet watcher: FSWatcher;\nconst watchQueue = new Set<string>();\nlet hasStarted = false;\n\nexport function enable({ enableGlobbing }: { enableGlobbing: boolean }) {\n  isWatchMode = true;\n\n  const { FSWatcher } = requireChokidar();\n\n  const options: WatchOptions = {\n    disableGlobbing: !enableGlobbing,\n    persistent: true,\n    ignoreInitial: true,\n    awaitWriteFinish: {\n      stabilityThreshold: 50,\n      pollInterval: 10,\n    },\n  };\n  watcher = new FSWatcher(options);\n\n  watcher.on(\"unlink\", unwatchFile);\n}\n\nexport function startWatcher() {\n  hasStarted = true;\n\n  for (const dep of watchQueue) {\n    watcher.add(dep);\n  }\n  watchQueue.clear();\n\n  watcher.on(\"ready\", () => {\n    console.log(\"The watcher is ready.\");\n  });\n}\n\nexport function watch(filename: string): void {\n  if (!isWatchMode) {\n    throw new Error(\n      \"Internal Babel error: .watch called when not in watch mode.\",\n    );\n  }\n\n  if (!hasStarted) {\n    watchQueue.add(path.resolve(filename));\n  } else {\n    watcher.add(path.resolve(filename));\n  }\n}\n\n/**\n * Call @param callback whenever a dependency (source file)/\n * external dependency (non-source file) changes.\n *\n * Handles mapping external dependencies to their corresponding\n * dependencies.\n */\nexport function onFilesChange(\n  callback: (filenames: string[], event: string, cause: string) => void,\n): void {\n  if (!isWatchMode) {\n    throw new Error(\n      \"Internal Babel error: .onFilesChange called when not in watch mode.\",\n    );\n  }\n\n  watcher.on(\"all\", (event, filename) => {\n    if (event !== \"change\" && event !== \"add\") return;\n\n    const absoluteFile = path.resolve(filename);\n    callback(\n      [absoluteFile, ...(depToFiles.get(absoluteFile) ?? [])],\n      event,\n      absoluteFile,\n    );\n  });\n}\n\nexport function updateExternalDependencies(\n  filename: string,\n  dependencies: Set<string>,\n) {\n  if (!isWatchMode) return;\n\n  // Use absolute paths\n  const absFilename = path.resolve(filename);\n  const absDependencies = new Set(\n    Array.from(dependencies, dep => path.resolve(dep)),\n  );\n\n  const deps = fileToDeps.get(absFilename);\n  if (deps) {\n    for (const dep of deps) {\n      if (!absDependencies.has(dep)) {\n        removeFileDependency(absFilename, dep);\n      }\n    }\n  }\n  for (const dep of absDependencies) {\n    let deps = depToFiles.get(dep);\n    if (!deps) {\n      depToFiles.set(dep, (deps = new Set()));\n\n      if (!hasStarted) {\n        watchQueue.add(dep);\n      } else {\n        watcher.add(dep);\n      }\n    }\n\n    deps.add(absFilename);\n  }\n\n  fileToDeps.set(absFilename, absDependencies);\n}\n\nfunction removeFileDependency(filename: string, dep: string) {\n  const deps = depToFiles.get(dep);\n  deps.delete(filename);\n\n  if (deps.size === 0) {\n    depToFiles.delete(dep);\n\n    if (!hasStarted) {\n      watchQueue.delete(dep);\n    } else {\n      watcher.unwatch(dep);\n    }\n  }\n}\n\nfunction unwatchFile(filename: string) {\n  const deps = fileToDeps.get(filename);\n  if (!deps) return;\n\n  for (const dep of deps) {\n    removeFileDependency(filename, dep);\n  }\n  fileToDeps.delete(filename);\n}\n\nfunction requireChokidar(): any {\n  const require = createRequire(import.meta.url);\n\n  try {\n    return process.env.BABEL_8_BREAKING\n      ? require(\"chokidar\")\n      : parseInt(process.versions.node) >= 8\n        ? require(\"chokidar\")\n        : require(\"@nicolo-ribaudo/chokidar-2\");\n  } catch (err) {\n    console.error(\n      \"The optional dependency chokidar failed to install and is required for \" +\n        \"--watch. Chokidar is likely not supported on your platform.\",\n    );\n    throw err;\n  }\n}\n"], "mappings": ";;;;;;;;;;AACA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,MAAME,UAAU,GAAG,IAAIC,GAAG,CAAsB,CAAC;AACjD,MAAMC,UAAU,GAAG,IAAID,GAAG,CAAsB,CAAC;AAEjD,IAAIE,WAAW,GAAG,KAAK;AACvB,IAAIC,OAAkB;AACtB,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAS,CAAC;AACpC,IAAIC,UAAU,GAAG,KAAK;AAEf,SAASC,MAAMA,CAAC;EAAEC;AAA4C,CAAC,EAAE;EACtEN,WAAW,GAAG,IAAI;EAElB,MAAM;IAAEO;EAAU,CAAC,GAAGC,eAAe,CAAC,CAAC;EAEvC,MAAMC,OAAqB,GAAG;IAC5BC,eAAe,EAAE,CAACJ,cAAc;IAChCK,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtBC,YAAY,EAAE;IAChB;EACF,CAAC;EACDd,OAAO,GAAG,IAAIM,SAAS,CAACE,OAAO,CAAC;EAEhCR,OAAO,CAACe,EAAE,CAAC,QAAQ,EAAEC,WAAW,CAAC;AACnC;AAEO,SAASC,YAAYA,CAAA,EAAG;EAC7Bd,UAAU,GAAG,IAAI;EAEjB,KAAK,MAAMe,GAAG,IAAIjB,UAAU,EAAE;IAC5BD,OAAO,CAACmB,GAAG,CAACD,GAAG,CAAC;EAClB;EACAjB,UAAU,CAACmB,KAAK,CAAC,CAAC;EAElBpB,OAAO,CAACe,EAAE,CAAC,OAAO,EAAE,MAAM;IACxBM,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC,CAAC,CAAC;AACJ;AAEO,SAASC,KAAKA,CAACC,QAAgB,EAAQ;EAC5C,IAAI,CAACzB,WAAW,EAAE;IAChB,MAAM,IAAI0B,KAAK,CACb,6DACF,CAAC;EACH;EAEA,IAAI,CAACtB,UAAU,EAAE;IACfF,UAAU,CAACkB,GAAG,CAACO,MAAGA,CAAC,CAACC,OAAO,CAACH,QAAQ,CAAC,CAAC;EACxC,CAAC,MAAM;IACLxB,OAAO,CAACmB,GAAG,CAACO,MAAGA,CAAC,CAACC,OAAO,CAACH,QAAQ,CAAC,CAAC;EACrC;AACF;AASO,SAASI,aAAaA,CAC3BC,QAAqE,EAC/D;EACN,IAAI,CAAC9B,WAAW,EAAE;IAChB,MAAM,IAAI0B,KAAK,CACb,qEACF,CAAC;EACH;EAEAzB,OAAO,CAACe,EAAE,CAAC,KAAK,EAAE,CAACe,KAAK,EAAEN,QAAQ,KAAK;IAAA,IAAAO,eAAA;IACrC,IAAID,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,KAAK,EAAE;IAE3C,MAAME,YAAY,GAAGN,MAAGA,CAAC,CAACC,OAAO,CAACH,QAAQ,CAAC;IAC3CK,QAAQ,CACN,CAACG,YAAY,EAAE,KAAAD,eAAA,GAAIjC,UAAU,CAACmC,GAAG,CAACD,YAAY,CAAC,YAAAD,eAAA,GAAI,EAAE,CAAC,CAAC,EACvDD,KAAK,EACLE,YACF,CAAC;EACH,CAAC,CAAC;AACJ;AAEO,SAASE,0BAA0BA,CACxCV,QAAgB,EAChBW,YAAyB,EACzB;EACA,IAAI,CAACpC,WAAW,EAAE;EAGlB,MAAMqC,WAAW,GAAGV,MAAGA,CAAC,CAACC,OAAO,CAACH,QAAQ,CAAC;EAC1C,MAAMa,eAAe,GAAG,IAAInC,GAAG,CAC7BoC,KAAK,CAACC,IAAI,CAACJ,YAAY,EAAEjB,GAAG,IAAIQ,MAAGA,CAAC,CAACC,OAAO,CAACT,GAAG,CAAC,CACnD,CAAC;EAED,MAAMsB,IAAI,GAAG5C,UAAU,CAACqC,GAAG,CAACG,WAAW,CAAC;EACxC,IAAII,IAAI,EAAE;IACR,KAAK,MAAMtB,GAAG,IAAIsB,IAAI,EAAE;MACtB,IAAI,CAACH,eAAe,CAACI,GAAG,CAACvB,GAAG,CAAC,EAAE;QAC7BwB,oBAAoB,CAACN,WAAW,EAAElB,GAAG,CAAC;MACxC;IACF;EACF;EACA,KAAK,MAAMA,GAAG,IAAImB,eAAe,EAAE;IACjC,IAAIG,IAAI,GAAG1C,UAAU,CAACmC,GAAG,CAACf,GAAG,CAAC;IAC9B,IAAI,CAACsB,IAAI,EAAE;MACT1C,UAAU,CAAC6C,GAAG,CAACzB,GAAG,EAAGsB,IAAI,GAAG,IAAItC,GAAG,CAAC,CAAE,CAAC;MAEvC,IAAI,CAACC,UAAU,EAAE;QACfF,UAAU,CAACkB,GAAG,CAACD,GAAG,CAAC;MACrB,CAAC,MAAM;QACLlB,OAAO,CAACmB,GAAG,CAACD,GAAG,CAAC;MAClB;IACF;IAEAsB,IAAI,CAACrB,GAAG,CAACiB,WAAW,CAAC;EACvB;EAEAxC,UAAU,CAAC+C,GAAG,CAACP,WAAW,EAAEC,eAAe,CAAC;AAC9C;AAEA,SAASK,oBAAoBA,CAAClB,QAAgB,EAAEN,GAAW,EAAE;EAC3D,MAAMsB,IAAI,GAAG1C,UAAU,CAACmC,GAAG,CAACf,GAAG,CAAC;EAChCsB,IAAI,CAACI,MAAM,CAACpB,QAAQ,CAAC;EAErB,IAAIgB,IAAI,CAACK,IAAI,KAAK,CAAC,EAAE;IACnB/C,UAAU,CAAC8C,MAAM,CAAC1B,GAAG,CAAC;IAEtB,IAAI,CAACf,UAAU,EAAE;MACfF,UAAU,CAAC2C,MAAM,CAAC1B,GAAG,CAAC;IACxB,CAAC,MAAM;MACLlB,OAAO,CAAC8C,OAAO,CAAC5B,GAAG,CAAC;IACtB;EACF;AACF;AAEA,SAASF,WAAWA,CAACQ,QAAgB,EAAE;EACrC,MAAMgB,IAAI,GAAG5C,UAAU,CAACqC,GAAG,CAACT,QAAQ,CAAC;EACrC,IAAI,CAACgB,IAAI,EAAE;EAEX,KAAK,MAAMtB,GAAG,IAAIsB,IAAI,EAAE;IACtBE,oBAAoB,CAAClB,QAAQ,EAAEN,GAAG,CAAC;EACrC;EACAtB,UAAU,CAACgD,MAAM,CAACpB,QAAQ,CAAC;AAC7B;AAEA,SAASjB,eAAeA,CAAA,EAAQ;EAG9B,IAAI;IACF,OAEIwC,QAAQ,CAACC,OAAO,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC,GAClCvD,OAAO,CAAC,UAAU,CAAC,GACnBA,OAAO,CAAC,4BAA4B,CAAC;EAC7C,CAAC,CAAC,OAAOwD,GAAG,EAAE;IACZ9B,OAAO,CAAC+B,KAAK,CACX,yEAAyE,GACvE,6DACJ,CAAC;IACD,MAAMD,GAAG;EACX;AACF", "ignoreList": []}