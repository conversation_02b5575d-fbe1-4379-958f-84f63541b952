{"dependencies": [{"name": "@stencil/core", "version": "2.10.0", "main": "compiler/stencil.js", "resources": ["package.json", "compiler/lib.d.ts", "compiler/lib.dom.d.ts", "compiler/lib.dom.iterable.d.ts", "compiler/lib.es2015.collection.d.ts", "compiler/lib.es2015.core.d.ts", "compiler/lib.es2015.d.ts", "compiler/lib.es2015.generator.d.ts", "compiler/lib.es2015.iterable.d.ts", "compiler/lib.es2015.promise.d.ts", "compiler/lib.es2015.proxy.d.ts", "compiler/lib.es2015.reflect.d.ts", "compiler/lib.es2015.symbol.d.ts", "compiler/lib.es2015.symbol.wellknown.d.ts", "compiler/lib.es2016.array.include.d.ts", "compiler/lib.es2016.d.ts", "compiler/lib.es2016.full.d.ts", "compiler/lib.es2017.d.ts", "compiler/lib.es2017.full.d.ts", "compiler/lib.es2017.intl.d.ts", "compiler/lib.es2017.object.d.ts", "compiler/lib.es2017.sharedmemory.d.ts", "compiler/lib.es2017.string.d.ts", "compiler/lib.es2017.typedarrays.d.ts", "compiler/lib.es2018.asyncgenerator.d.ts", "compiler/lib.es2018.asynciterable.d.ts", "compiler/lib.es2018.d.ts", "compiler/lib.es2018.full.d.ts", "compiler/lib.es2018.intl.d.ts", "compiler/lib.es2018.promise.d.ts", "compiler/lib.es2018.regexp.d.ts", "compiler/lib.es2019.array.d.ts", "compiler/lib.es2019.d.ts", "compiler/lib.es2019.full.d.ts", "compiler/lib.es2019.object.d.ts", "compiler/lib.es2019.string.d.ts", "compiler/lib.es2019.symbol.d.ts", "compiler/lib.es2020.bigint.d.ts", "compiler/lib.es2020.d.ts", "compiler/lib.es2020.full.d.ts", "compiler/lib.es2020.intl.d.ts", "compiler/lib.es2020.promise.d.ts", "compiler/lib.es2020.sharedmemory.d.ts", "compiler/lib.es2020.string.d.ts", "compiler/lib.es2020.symbol.wellknown.d.ts", "compiler/lib.es2021.d.ts", "compiler/lib.es2021.full.d.ts", "compiler/lib.es2021.promise.d.ts", "compiler/lib.es2021.string.d.ts", "compiler/lib.es2021.weakref.d.ts", "compiler/lib.es5.d.ts", "compiler/lib.es6.d.ts", "compiler/lib.esnext.d.ts", "compiler/lib.esnext.full.d.ts", "compiler/lib.esnext.intl.d.ts", "compiler/lib.esnext.promise.d.ts", "compiler/lib.esnext.string.d.ts", "compiler/lib.esnext.weakref.d.ts", "compiler/lib.scripthost.d.ts", "compiler/lib.webworker.d.ts", "compiler/lib.webworker.importscripts.d.ts", "compiler/lib.webworker.iterable.d.ts", "internal/index.d.ts", "internal/index.js", "internal/package.json", "internal/stencil-ext-modules.d.ts", "internal/stencil-private.d.ts", "internal/stencil-public-compiler.d.ts", "internal/stencil-public-docs.d.ts", "internal/stencil-public-runtime.d.ts", "mock-doc/index.js", "mock-doc/package.json", "internal/client/css-shim.js", "internal/client/dom.js", "internal/client/index.js", "internal/client/package.json", "internal/client/patch-browser.js", "internal/client/patch-esm.js", "internal/client/shadow-css.js", "internal/hydrate/index.js", "internal/hydrate/package.json", "internal/hydrate/runner.js", "internal/hydrate/shadow-css.js", "internal/stencil-core/index.d.ts", "internal/stencil-core/index.js"]}, {"name": "rollup", "main": "dist/es/rollup.browser.js"}, {"name": "terser", "main": "dist/bundle.min.js"}, {"name": "typescript", "main": "lib/typescript.js"}]}