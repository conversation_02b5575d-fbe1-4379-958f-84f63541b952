{"name": "@primer/octicons", "version": "16.3.1", "description": "A scalable set of icons handcrafted with <3 by GitHub.", "homepage": "https://primer.style/octicons", "author": "GitHub Inc.", "license": "MIT", "style": "index.scss", "main": "index.js", "files": ["index.js", "index.scss", "build"], "repository": "https://github.com/primer/octicons.git", "bugs": {"url": "https://github.com/primer/octicons/issues"}, "scripts": {"build": "\\cp -r ../build/ ./build && \\cp index.scss ./build/build.css", "lint": "eslint index.js tests/*.js", "test": "ava --verbose 'tests/*.js'"}, "keywords": ["GitHub", "icons", "svg", "octicons"], "dependencies": {"object-assign": "^4.1.1"}, "devDependencies": {"@github/prettier-config": "0.0.4", "ava": "^0.22.0", "eslint": "^6.5.1", "eslint-plugin-github": "4.1.3"}, "eslintConfig": {"extends": ["plugin:github/internal", "plugin:github/recommended"], "env": {"es6": true, "node": true}, "parserOptions": {"ecmaVersion": 2017, "requireConfigFile": false}, "rules": {"github/no-then": 0}}}